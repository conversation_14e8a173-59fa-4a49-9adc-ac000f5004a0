/**
 * Simple Tooltip Component for Disc Golf Inventory Management System
 * 
 * A lightweight tooltip implementation using CSS and HTML title attribute
 * for accessibility and simplicity.
 */

import * as React from "react"
import { cn } from "@/lib/utils"

/**
 * Props for the Tooltip component
 */
export interface TooltipProps {
  /** The content to show in the tooltip */
  content: string
  /** The trigger element that shows the tooltip on hover */
  children: React.ReactNode
  /** Additional CSS classes */
  className?: string
  /** Position of the tooltip relative to the trigger */
  position?: "top" | "bottom" | "left" | "right"
}

/**
 * Simple tooltip component using CSS hover states
 * 
 * @example
 * ```tsx
 * <Tooltip content="This is a tooltip">
 *   <button>Hover me</button>
 * </Tooltip>
 * ```
 */
function Tooltip({ 
  content, 
  children, 
  className,
  position = "top"
}: TooltipProps) {
  return (
    <div className={cn("relative inline-block group", className)}>
      {children}
      <div
        role="tooltip"
        className={cn(
          // Base tooltip styles
          "absolute z-50 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg",
          "opacity-0 invisible group-hover:opacity-100 group-hover:visible",
          "transition-all duration-200 ease-in-out",
          "pointer-events-none whitespace-nowrap",
          // Position-specific styles
          {
            "bottom-full left-1/2 transform -translate-x-1/2 mb-1": position === "top",
            "top-full left-1/2 transform -translate-x-1/2 mt-1": position === "bottom", 
            "right-full top-1/2 transform -translate-y-1/2 mr-1": position === "left",
            "left-full top-1/2 transform -translate-y-1/2 ml-1": position === "right",
          }
        )}
      >
        {content}
        {/* Arrow */}
        <div
          className={cn(
            "absolute w-2 h-2 bg-gray-900 transform rotate-45",
            {
              "top-full left-1/2 transform -translate-x-1/2 -translate-y-1/2": position === "top",
              "bottom-full left-1/2 transform -translate-x-1/2 translate-y-1/2": position === "bottom",
              "left-full top-1/2 transform -translate-y-1/2 -translate-x-1/2": position === "left", 
              "right-full top-1/2 transform -translate-y-1/2 translate-x-1/2": position === "right",
            }
          )}
        />
      </div>
    </div>
  )
}

export { Tooltip }
