/**
 * FlightNumbersDisplay Component for Disc Golf Inventory Management System
 *
 * Component to display disc flight characteristics with visual representation,
 * tooltips explaining each number, responsive layout, and screen reader accessibility.
 */

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { Tooltip } from "./tooltip";
import { cn } from "@/lib/utils";
import { FlightNumbers, FLIGHT_NUMBER_RANGES } from "@/lib/types";

/**
 * Flight numbers display variants for different sizes
 */
const flightDisplayVariants = cva("inline-flex items-center gap-1 font-mono font-medium", {
  variants: {
    size: {
      sm: "text-xs",
      md: "text-sm",
      lg: "text-base",
    },
  },
  defaultVariants: {
    size: "md",
  },
});

/**
 * Individual flight number variants
 */
const flightNumberVariants = cva(
  "inline-flex items-center justify-center rounded border font-mono font-semibold transition-colors",
  {
    variants: {
      size: {
        sm: "w-6 h-6 text-xs",
        md: "w-8 h-8 text-sm",
        lg: "w-10 h-10 text-base",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
);

/**
 * Props for the FlightNumbersDisplay component
 */
export interface FlightNumbersDisplayProps extends VariantProps<typeof flightDisplayVariants> {
  /** Flight numbers to display */
  flightNumbers: FlightNumbers;
  /** Whether to show labels for each number */
  showLabels?: boolean;
  /** Whether to show tooltips explaining each number */
  showTooltips?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Flight number explanations for tooltips
 */
const FLIGHT_EXPLANATIONS = {
  speed: "Speed (1-14): How fast the disc needs to be thrown to achieve its intended flight",
  glide: "Glide (1-7): How long the disc stays in the air and maintains its forward momentum",
  turn: "Turn (-5 to +1): High speed stability - negative numbers turn right, positive resist turning",
  fade: "Fade (0-5): Low speed stability - how much the disc hooks left at the end of flight",
} as const;

/**
 * Get color classes for flight number based on its value and type
 */
function getFlightNumberColor(type: keyof FlightNumbers, value: number): string {
  const range = FLIGHT_NUMBER_RANGES[type];
  const percentage = (value - range.min) / (range.max - range.min);

  // Color coding based on typical disc golf conventions
  switch (type) {
    case "speed":
      if (percentage < 0.3) return "bg-blue-100 text-blue-800 border-blue-200";
      if (percentage < 0.7) return "bg-green-100 text-green-800 border-green-200";
      return "bg-red-100 text-red-800 border-red-200";
    case "glide":
      if (percentage < 0.4) return "bg-orange-100 text-orange-800 border-orange-200";
      if (percentage < 0.8) return "bg-green-100 text-green-800 border-green-200";
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "turn":
      if (value < -2) return "bg-red-100 text-red-800 border-red-200";
      if (value < 0) return "bg-yellow-100 text-yellow-800 border-yellow-200";
      return "bg-green-100 text-green-800 border-green-200";
    case "fade":
      if (percentage < 0.3) return "bg-blue-100 text-blue-800 border-blue-200";
      if (percentage < 0.7) return "bg-green-100 text-green-800 border-green-200";
      return "bg-orange-100 text-orange-800 border-orange-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}

/**
 * Individual flight number component
 */
function FlightNumber({
  type,
  value,
  size,
  showTooltip = true,
}: {
  type: keyof FlightNumbers;
  value: number;
  size?: "sm" | "md" | "lg";
  showTooltip?: boolean;
}) {
  const colorClasses = getFlightNumberColor(type, value);
  const explanation = FLIGHT_EXPLANATIONS[type];

  const numberElement = (
    <div className={cn(flightNumberVariants({ size }), colorClasses)} aria-label={`${type}: ${value}`}>
      {value}
    </div>
  );

  if (showTooltip) {
    return <Tooltip content={explanation}>{numberElement}</Tooltip>;
  }

  return numberElement;
}

/**
 * FlightNumbersDisplay component for showing disc flight characteristics
 *
 * @example
 * ```tsx
 * <FlightNumbersDisplay
 *   flightNumbers={{ speed: 12, glide: 5, turn: -1, fade: 3 }}
 *   size="md"
 *   showLabels={true}
 *   showTooltips={true}
 * />
 * ```
 */
function FlightNumbersDisplay({
  flightNumbers,
  size = "md",
  showLabels = false,
  showTooltips = true,
  className,
}: FlightNumbersDisplayProps) {
  const { speed, glide, turn, fade } = flightNumbers;

  return (
    <div className={cn(flightDisplayVariants({ size }), className)} role="group" aria-label="Flight numbers">
      {showLabels && <span className="text-muted-foreground mr-2 font-sans font-normal">Flight:</span>}

      <FlightNumber type="speed" value={speed} size={size || undefined} showTooltip={showTooltips} />
      <span className="text-muted-foreground">|</span>
      <FlightNumber type="glide" value={glide} size={size || undefined} showTooltip={showTooltips} />
      <span className="text-muted-foreground">|</span>
      <FlightNumber type="turn" value={turn} size={size || undefined} showTooltip={showTooltips} />
      <span className="text-muted-foreground">|</span>
      <FlightNumber type="fade" value={fade} size={size || undefined} showTooltip={showTooltips} />

      {/* Screen reader description */}
      <span className="sr-only">
        Flight characteristics: Speed {speed}, Glide {glide}, Turn {turn}, Fade {fade}
      </span>
    </div>
  );
}

export { FlightNumbersDisplay, flightDisplayVariants };
