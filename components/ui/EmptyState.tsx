/**
 * EmptyState Component for Disc Golf Inventory Management System
 *
 * A component for displaying empty collection states with friendly messages,
 * call-to-action buttons, illustrations/icons, and responsive design.
 */

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Plus, Search, Disc3, Package } from "lucide-react";

/**
 * Props for the EmptyState component
 */
export interface EmptyStateProps {
  /** The main title/heading for the empty state */
  title?: string;
  /** Descriptive text explaining the empty state */
  description?: string;
  /** Icon to display - can be a Lucide icon component or custom element */
  icon?: React.ComponentType<{ className?: string }> | React.ReactNode;
  /** Primary action button text */
  actionLabel?: string;
  /** Handler for the primary action button */
  onAction?: () => void;
  /** Secondary action button text */
  secondaryActionLabel?: string;
  /** Handler for the secondary action button */
  onSecondaryAction?: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Size variant for the empty state */
  size?: "sm" | "md" | "lg";
  /** Variant for different empty state types */
  variant?: "default" | "search" | "error" | "loading";
}

/**
 * Default empty state configurations for different scenarios
 */
const EMPTY_STATE_CONFIGS = {
  default: {
    title: "No discs in your collection",
    description: "Start building your disc golf inventory by adding your first disc.",
    icon: Disc3,
    actionLabel: "Add Your First Disc",
  },
  search: {
    title: "No discs found",
    description: "Try adjusting your search criteria or filters to find what you're looking for.",
    icon: Search,
    actionLabel: "Clear Filters",
  },
  error: {
    title: "Something went wrong",
    description: "We couldn't load your disc collection. Please try again.",
    icon: Package,
    actionLabel: "Try Again",
  },
  loading: {
    title: "Loading your collection",
    description: "Please wait while we fetch your discs...",
    icon: Disc3,
    actionLabel: undefined,
  },
} as const;

/**
 * EmptyState component for displaying empty collection states
 *
 * @example
 * ```tsx
 * // Default empty collection
 * <EmptyState onAction={() => navigate('/add-disc')} />
 *
 * // Custom empty state
 * <EmptyState
 *   title="No favorites yet"
 *   description="Mark discs as favorites to see them here"
 *   icon={Heart}
 *   actionLabel="Browse Collection"
 *   onAction={() => navigate('/inventory')}
 * />
 *
 * // Search results empty state
 * <EmptyState
 *   variant="search"
 *   onAction={clearFilters}
 *   secondaryActionLabel="Add New Disc"
 *   onSecondaryAction={() => navigate('/add-disc')}
 * />
 * ```
 */
function EmptyState({
  title,
  description,
  icon,
  actionLabel,
  onAction,
  secondaryActionLabel,
  onSecondaryAction,
  className,
  size = "md",
  variant = "default",
}: EmptyStateProps) {
  // Get default configuration for the variant
  const config = EMPTY_STATE_CONFIGS[variant];

  // Use provided props or fall back to variant defaults
  const finalTitle = title ?? config.title;
  const finalDescription = description ?? config.description;
  const finalIcon = icon ?? config.icon;
  const finalActionLabel = actionLabel ?? config.actionLabel;

  // Render the icon
  const renderIcon = () => {
    if (React.isValidElement(finalIcon)) {
      return finalIcon;
    }

    if (typeof finalIcon === "function") {
      const IconComponent = finalIcon as React.ComponentType<{ className?: string }>;
      return (
        <IconComponent
          className={cn("text-muted-foreground/50", {
            "h-12 w-12": size === "sm",
            "h-16 w-16": size === "md",
            "h-20 w-20": size === "lg",
          })}
        />
      );
    }

    return null;
  };

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center text-center",
        {
          "py-8 px-4": size === "sm",
          "py-12 px-6": size === "md",
          "py-16 px-8": size === "lg",
        },
        className
      )}
      role="status"
      aria-live="polite"
    >
      {/* Icon */}
      <div className="mb-4">{renderIcon()}</div>

      {/* Title */}
      <h3
        className={cn("font-semibold text-foreground mb-2", {
          "text-lg": size === "sm",
          "text-xl": size === "md",
          "text-2xl": size === "lg",
        })}
      >
        {finalTitle}
      </h3>

      {/* Description */}
      <p
        className={cn("text-muted-foreground mb-6 max-w-md", {
          "text-sm": size === "sm",
          "text-base": size === "md",
          "text-lg": size === "lg",
        })}
      >
        {finalDescription}
      </p>

      {/* Action Buttons */}
      {(finalActionLabel || secondaryActionLabel) && (
        <div className="flex flex-col sm:flex-row gap-3">
          {finalActionLabel && onAction && (
            <Button onClick={onAction} size={size === "sm" ? "sm" : "default"} className="min-w-[140px]">
              {variant === "default" && <Plus className="h-4 w-4 mr-2" />}
              {finalActionLabel}
            </Button>
          )}

          {secondaryActionLabel && onSecondaryAction && (
            <Button
              variant="outline"
              onClick={onSecondaryAction}
              size={size === "sm" ? "sm" : "default"}
              className="min-w-[140px]"
            >
              {secondaryActionLabel}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

export { EmptyState };
