/**
 * Footer Component for the Disc Golf Inventory Management System
 * 
 * This component provides the main footer with basic information,
 * links, and proper semantic HTML structure.
 */

import React from 'react';
import Link from 'next/link';
import { Github, Heart, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Footer link interface
 */
interface FooterLink {
  label: string;
  href: string;
  external?: boolean;
  description?: string;
}

/**
 * Footer section interface
 */
interface FooterSection {
  title: string;
  links: FooterLink[];
}

/**
 * Footer component props
 */
interface FooterProps {
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the full footer or minimal version */
  minimal?: boolean;
  /** Custom footer sections */
  sections?: FooterSection[];
}

// ============================================================================
// FOOTER DATA
// ============================================================================

/**
 * Default footer sections for the disc golf inventory app
 */
const defaultFooterSections: FooterSection[] = [
  {
    title: 'Inventory',
    links: [
      { label: 'View Collection', href: '/inventory', description: 'Browse your disc collection' },
      { label: 'Add New Disc', href: '/add-disc', description: 'Add a disc to your inventory' },
      { label: 'Search & Filter', href: '/inventory?search=true', description: 'Find specific discs' },
      { label: 'Statistics', href: '/stats', description: 'View collection insights' }
    ]
  },
  {
    title: 'Tools',
    links: [
      { label: 'Export Data', href: '/export', description: 'Download your collection data' },
      { label: 'Import Data', href: '/import', description: 'Upload collection data' },
      { label: 'Backup', href: '/backup', description: 'Create data backup' },
      { label: 'Settings', href: '/settings', description: 'App preferences' }
    ]
  },
  {
    title: 'Resources',
    links: [
      { 
        label: 'PDGA', 
        href: 'https://www.pdga.com', 
        external: true, 
        description: 'Professional Disc Golf Association' 
      },
      { 
        label: 'Flight Guide', 
        href: 'https://www.pdga.com/technical-standards/equipment-certification/discs/flight-ratings-system', 
        external: true, 
        description: 'Understanding flight numbers' 
      },
      { 
        label: 'Disc Reviews', 
        href: 'https://infinitediscs.com', 
        external: true, 
        description: 'Disc reviews and ratings' 
      }
    ]
  }
];

// ============================================================================
// FOOTER COMPONENT
// ============================================================================

/**
 * Main footer component with responsive layout
 */
export function Footer({
  className,
  minimal = false,
  sections = defaultFooterSections
}: FooterProps) {
  const currentYear = new Date().getFullYear();

  if (minimal) {
    return (
      <footer className={cn('border-t bg-background', className)}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6 text-center">
            <p className="text-sm text-muted-foreground">
              © {currentYear} Disc Golf Inventory. Built with{' '}
              <Heart className="inline h-4 w-4 text-red-500" aria-label="love" />{' '}
              for disc golf enthusiasts.
            </p>
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className={cn('border-t bg-background', className)}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-2 mb-4">
                <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-sm">DG</span>
                </div>
                <span className="text-lg font-bold text-foreground">
                  Disc Golf Inventory
                </span>
              </div>
              <p className="text-sm text-muted-foreground mb-4 max-w-xs">
                A comprehensive personal disc golf inventory management system to help you 
                organize and optimize your disc collection.
              </p>
              <div className="flex items-center space-x-4">
                <Link
                  href="https://github.com"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                  aria-label="GitHub Repository"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Github className="h-5 w-5" />
                </Link>
              </div>
            </div>

            {/* Footer Sections */}
            {sections.map((section) => (
              <div key={section.title} className="space-y-4">
                <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider">
                  {section.title}
                </h3>
                <ul className="space-y-3">
                  {section.links.map((link) => (
                    <li key={link.href}>
                      <Link
                        href={link.href}
                        className={cn(
                          'text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-1',
                          link.external && 'group'
                        )}
                        title={link.description}
                        {...(link.external && {
                          target: '_blank',
                          rel: 'noopener noreferrer'
                        })}
                      >
                        <span>{link.label}</span>
                        {link.external && (
                          <ExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                        )}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="border-t py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm text-muted-foreground">
              <p>
                © {currentYear} Disc Golf Inventory. All rights reserved.
              </p>
              <div className="flex items-center space-x-4">
                <Link
                  href="/privacy"
                  className="hover:text-foreground transition-colors"
                >
                  Privacy Policy
                </Link>
                <Link
                  href="/terms"
                  className="hover:text-foreground transition-colors"
                >
                  Terms of Service
                </Link>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <span>Built with</span>
              <Heart className="h-4 w-4 text-red-500" aria-label="love" />
              <span>for disc golf enthusiasts</span>
            </div>
          </div>
        </div>

        {/* Data Privacy Notice */}
        <div className="border-t py-4">
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              🔒 Your data stays private - all inventory information is stored locally on your device.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}

// ============================================================================
// EXPORTS
// ============================================================================

export default Footer;
