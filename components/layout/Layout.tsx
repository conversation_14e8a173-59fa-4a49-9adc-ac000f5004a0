/**
 * Main Layout Component for the Disc Golf Inventory Management System
 * 
 * This component provides the main application layout structure combining
 * Header, Footer, and main content area with proper semantic HTML.
 */

'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { Header } from './Header';
import { Footer } from './Footer';
import { cn } from '@/lib/utils';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Layout component props
 */
interface LayoutProps {
  /** Child components to render in the main content area */
  children: React.ReactNode;
  /** Additional CSS classes for the layout container */
  className?: string;
  /** Whether to show the header */
  showHeader?: boolean;
  /** Whether to show the footer */
  showFooter?: boolean;
  /** Whether to show search functionality in header */
  showSearch?: boolean;
  /** Whether to use minimal footer */
  minimalFooter?: boolean;
  /** Whether to use full-width layout (no container padding) */
  fullWidth?: boolean;
  /** Additional CSS classes for the main content area */
  mainClassName?: string;
}

// ============================================================================
// LAYOUT COMPONENT
// ============================================================================

/**
 * Main layout component with header, footer, and content area
 */
export function Layout({
  children,
  className,
  showHeader = true,
  showFooter = true,
  showSearch = true,
  minimalFooter = false,
  fullWidth = false,
  mainClassName
}: LayoutProps) {
  const pathname = usePathname();

  return (
    <div className={cn('min-h-screen flex flex-col bg-background', className)}>
      {/* Header */}
      {showHeader && (
        <Header
          showSearch={showSearch}
          currentPath={pathname}
        />
      )}

      {/* Main Content Area */}
      <main
        className={cn(
          'flex-1',
          !fullWidth && 'container mx-auto px-4 sm:px-6 lg:px-8',
          mainClassName
        )}
        role="main"
      >
        {children}
      </main>

      {/* Footer */}
      {showFooter && (
        <Footer minimal={minimalFooter} />
      )}
    </div>
  );
}

// ============================================================================
// SPECIALIZED LAYOUT VARIANTS
// ============================================================================

/**
 * Layout for full-width pages (like landing pages)
 */
export function FullWidthLayout({
  children,
  className,
  ...props
}: Omit<LayoutProps, 'fullWidth'>) {
  return (
    <Layout
      {...props}
      fullWidth={true}
      className={className}
    >
      {children}
    </Layout>
  );
}

/**
 * Layout for minimal pages (like auth pages)
 */
export function MinimalLayout({
  children,
  className,
  ...props
}: LayoutProps) {
  return (
    <Layout
      {...props}
      showHeader={false}
      minimalFooter={true}
      className={cn('justify-center items-center', className)}
      mainClassName="flex flex-col justify-center items-center min-h-[calc(100vh-200px)]"
    >
      {children}
    </Layout>
  );
}

/**
 * Layout for content pages with sidebar potential
 */
export function ContentLayout({
  children,
  className,
  sidebar,
  sidebarPosition = 'left',
  ...props
}: LayoutProps & {
  sidebar?: React.ReactNode;
  sidebarPosition?: 'left' | 'right';
}) {
  if (!sidebar) {
    return (
      <Layout {...props} className={className}>
        <div className="py-6">
          {children}
        </div>
      </Layout>
    );
  }

  return (
    <Layout {...props} className={className}>
      <div className="py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          {sidebarPosition === 'left' && (
            <aside className="lg:col-span-1">
              <div className="sticky top-20">
                {sidebar}
              </div>
            </aside>
          )}

          {/* Main Content */}
          <div className={cn(
            sidebar ? 'lg:col-span-3' : 'lg:col-span-4'
          )}>
            {children}
          </div>

          {/* Right Sidebar */}
          {sidebarPosition === 'right' && (
            <aside className="lg:col-span-1">
              <div className="sticky top-20">
                {sidebar}
              </div>
            </aside>
          )}
        </div>
      </div>
    </Layout>
  );
}

// ============================================================================
// LAYOUT UTILITIES
// ============================================================================

/**
 * Page container component for consistent spacing
 */
export function PageContainer({
  children,
  className,
  title,
  description,
  actions
}: {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  actions?: React.ReactNode;
}) {
  return (
    <div className={cn('py-6 space-y-6', className)}>
      {/* Page Header */}
      {(title || description || actions) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="space-y-1">
            {title && (
              <h1 className="text-2xl font-bold text-foreground">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-muted-foreground">
                {description}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex items-center space-x-2">
              {actions}
            </div>
          )}
        </div>
      )}

      {/* Page Content */}
      <div>
        {children}
      </div>
    </div>
  );
}

/**
 * Section component for organizing page content
 */
export function Section({
  children,
  className,
  title,
  description
}: {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
}) {
  return (
    <section className={cn('space-y-4', className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h2 className="text-lg font-semibold text-foreground">
              {title}
            </h2>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}
      <div>
        {children}
      </div>
    </section>
  );
}

// ============================================================================
// EXPORTS
// ============================================================================

export default Layout;
