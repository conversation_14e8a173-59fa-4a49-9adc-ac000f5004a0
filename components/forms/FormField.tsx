/**
 * FormField Component for Disc Golf Inventory Management System
 *
 * A reusable form field component that provides label and input association,
 * error message display, required field indicators, and accessible markup
 * following WCAG guidelines and professional form design patterns.
 */

import * as React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { AlertCircle, Asterisk } from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Props for the FormField component
 */
export interface FormFieldProps {
  /** Unique identifier for the field */
  id: string;
  /** Field label text */
  label: string;
  /** Input type (defaults to "text") */
  type?: React.HTMLInputTypeAttribute;
  /** Input placeholder text */
  placeholder?: string;
  /** Current field value */
  value?: string | number;
  /** Change handler for the input */
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  /** Blur handler for the input */
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  /** Whether the field is required */
  required?: boolean;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Error message to display */
  error?: string;
  /** Help text to display below the field */
  helpText?: string;
  /** Additional CSS classes for the container */
  className?: string;
  /** Additional CSS classes for the input */
  inputClassName?: string;
  /** Additional CSS classes for the label */
  labelClassName?: string;
  /** ARIA describedby attribute value */
  "aria-describedby"?: string;
  /** Additional input props */
  inputProps?: Omit<React.ComponentProps<"input">, "id" | "type" | "value" | "onChange" | "onBlur" | "disabled" | "required" | "aria-describedby">;
}

// ============================================================================
// COMPONENT IMPLEMENTATION
// ============================================================================

/**
 * FormField component with label, validation, and accessibility features
 *
 * Features:
 * - Proper label-input association via htmlFor/id
 * - Required field visual indicators
 * - Error message display with ARIA support
 * - Help text support
 * - Accessible markup with proper ARIA attributes
 * - Responsive design
 * - Focus management
 *
 * @param props - FormField component props
 * @returns JSX element
 */
export function FormField({
  id,
  label,
  type = "text",
  placeholder,
  value,
  onChange,
  onBlur,
  required = false,
  disabled = false,
  error,
  helpText,
  className,
  inputClassName,
  labelClassName,
  "aria-describedby": ariaDescribedBy,
  inputProps,
}: FormFieldProps) {
  // Generate IDs for associated elements
  const errorId = error ? `${id}-error` : undefined;
  const helpId = helpText ? `${id}-help` : undefined;
  
  // Combine ARIA describedby attributes
  const describedBy = [ariaDescribedBy, errorId, helpId].filter(Boolean).join(" ") || undefined;

  return (
    <div className={cn("space-y-2", className)}>
      {/* Label with required indicator */}
      <Label
        htmlFor={id}
        className={cn(
          "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
          error && "text-destructive",
          labelClassName
        )}
      >
        {label}
        {required && (
          <Asterisk
            className="inline-block ml-1 size-3 text-destructive"
            aria-label="Required field"
          />
        )}
      </Label>

      {/* Input field */}
      <Input
        id={id}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        disabled={disabled}
        required={required}
        aria-invalid={error ? "true" : "false"}
        aria-describedby={describedBy}
        className={cn(
          error && "border-destructive focus-visible:ring-destructive/20",
          inputClassName
        )}
        {...inputProps}
      />

      {/* Error message */}
      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="flex items-center gap-2 text-sm text-destructive"
        >
          <AlertCircle className="size-4 shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {/* Help text */}
      {helpText && !error && (
        <div
          id={helpId}
          className="text-sm text-muted-foreground"
        >
          {helpText}
        </div>
      )}
    </div>
  );
}

// ============================================================================
// COMPONENT VARIANTS
// ============================================================================

/**
 * Props for FormFieldGroup component
 */
export interface FormFieldGroupProps {
  /** Group title */
  title?: string;
  /** Group description */
  description?: string;
  /** Child form fields */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
}

/**
 * FormFieldGroup component for grouping related form fields
 *
 * @param props - FormFieldGroup component props
 * @returns JSX element
 */
export function FormFieldGroup({
  title,
  description,
  children,
  className,
}: FormFieldGroupProps) {
  return (
    <fieldset className={cn("space-y-4", className)}>
      {title && (
        <legend className="text-base font-semibold leading-none tracking-tight">
          {title}
        </legend>
      )}
      {description && (
        <p className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </fieldset>
  );
}
