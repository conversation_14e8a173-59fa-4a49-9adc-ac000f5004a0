/**
 * Test suite for Filter Utilities
 *
 * Comprehensive tests for search and filter functionality including
 * text search, range filtering, multi-select filtering, and utility functions.
 */

import { describe, it, expect, beforeEach } from "vitest";
import type { Disc } from "../types";
import { DiscCondition, Location } from "../types";
import {
  enhancedSearchDiscs,
  filterByFlightRanges,
  filterByDateRanges,
  filterByMultiSelect,
  applyAllFilters,
  getUniqueFieldValues,
  getFilterSuggestions,
  hasActiveFilters,
  serializeFiltersToURL,
  deserializeFiltersFromURL,
  createEmptyFilterCriteria,
  type EnhancedFilterCriteria,
} from "../filterUtils";

// ============================================================================
// TEST DATA
// ============================================================================

const createTestDisc = (overrides: Partial<Disc> = {}): Disc => ({
  id: "test-id",
  manufacturer: "Innova",
  mold: "Destroyer",
  plasticType: "Champion",
  weight: 175,
  condition: DiscCondition.NEW,
  flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
  color: "Blue",
  notes: "Test disc",
  currentLocation: Location.BAG,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
  ...overrides,
});

const testDiscs: Disc[] = [
  createTestDisc({
    id: "1",
    manufacturer: "Innova",
    mold: "Destroyer",
    plasticType: "Champion",
    weight: 175,
    color: "Blue",
    flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
    condition: DiscCondition.NEW,
    currentLocation: Location.BAG,
  }),
  createTestDisc({
    id: "2",
    manufacturer: "Discraft",
    mold: "Buzzz",
    plasticType: "ESP",
    weight: 177,
    color: "Red",
    flightNumbers: { speed: 5, glide: 4, turn: -1, fade: 1 },
    condition: DiscCondition.LIGHTLY_USED,
    currentLocation: Location.HOME,
  }),
  createTestDisc({
    id: "3",
    manufacturer: "Dynamic Discs",
    mold: "Judge",
    plasticType: "Lucid",
    weight: 173,
    color: "Green",
    flightNumbers: { speed: 2, glide: 4, turn: 0, fade: 1 },
    condition: DiscCondition.WELL_USED,
    currentLocation: Location.CAR,
  }),
  createTestDisc({
    id: "4",
    manufacturer: "Innova",
    mold: "Roc",
    plasticType: "DX",
    weight: 180,
    color: "Orange",
    flightNumbers: { speed: 4, glide: 4, turn: 0, fade: 3 },
    condition: DiscCondition.BEAT_IN,
    currentLocation: Location.LOST,
  }),
];

// ============================================================================
// ENHANCED TEXT SEARCH TESTS
// ============================================================================

describe("enhancedSearchDiscs", () => {
  it("should return all discs when query is empty", () => {
    const result = enhancedSearchDiscs(testDiscs, "");
    expect(result).toHaveLength(testDiscs.length);
  });

  it("should search by manufacturer", () => {
    const result = enhancedSearchDiscs(testDiscs, "Innova");
    expect(result).toHaveLength(2);
    expect(result.every((disc) => disc.manufacturer === "Innova")).toBe(true);
  });

  it("should search by mold", () => {
    const result = enhancedSearchDiscs(testDiscs, "Buzzz");
    expect(result).toHaveLength(1);
    expect(result[0].mold).toBe("Buzzz");
  });

  it("should search case-insensitively by default", () => {
    const result = enhancedSearchDiscs(testDiscs, "destroyer");
    expect(result).toHaveLength(1);
    expect(result[0].mold).toBe("Destroyer");
  });

  it("should respect case sensitivity option", () => {
    const result = enhancedSearchDiscs(testDiscs, "destroyer", { caseSensitive: true });
    expect(result).toHaveLength(0);
  });

  it("should search across multiple fields", () => {
    const result = enhancedSearchDiscs(testDiscs, "Blue");
    expect(result).toHaveLength(1);
    expect(result[0].color).toBe("Blue");
  });

  it("should support word-based search", () => {
    const result = enhancedSearchDiscs(testDiscs, "Dynamic Judge", { wordBased: true });
    expect(result).toHaveLength(1);
    expect(result[0].manufacturer).toBe("Dynamic Discs");
    expect(result[0].mold).toBe("Judge");
  });

  it("should filter by specific fields", () => {
    const result = enhancedSearchDiscs(testDiscs, "Blue", { fields: ["color"] });
    expect(result).toHaveLength(1);
    expect(result[0].color).toBe("Blue");
  });
});

// ============================================================================
// RANGE FILTERING TESTS
// ============================================================================

describe("filterByFlightRanges", () => {
  it("should filter by speed range", () => {
    const result = filterByFlightRanges(testDiscs, { speed: { min: 10, max: 15 } });
    expect(result).toHaveLength(1);
    expect(result[0].flightNumbers.speed).toBe(12);
  });

  it("should filter by multiple flight number ranges", () => {
    const result = filterByFlightRanges(testDiscs, {
      speed: { min: 2, max: 5 },
      glide: { min: 4, max: 4 },
    });
    expect(result).toHaveLength(3); // Buzzz, Judge, and Roc (all have glide 4)
  });

  it("should return empty array when no discs match", () => {
    const result = filterByFlightRanges(testDiscs, { speed: { min: 20, max: 25 } });
    expect(result).toHaveLength(0);
  });
});

describe("filterByDateRanges", () => {
  it("should filter by creation date range", () => {
    const start = new Date("2023-12-01");
    const end = new Date("2024-02-01");
    const result = filterByDateRanges(testDiscs, { createdDate: { start, end } });
    expect(result).toHaveLength(testDiscs.length); // All created on 2024-01-01
  });

  it("should handle missing purchase dates", () => {
    const start = new Date("2023-01-01");
    const end = new Date("2024-12-31");
    const result = filterByDateRanges(testDiscs, { purchaseDate: { start, end } });
    expect(result).toHaveLength(testDiscs.length); // All discs pass when no purchase dates set
  });
});

// ============================================================================
// MULTI-SELECT FILTERING TESTS
// ============================================================================

describe("filterByMultiSelect", () => {
  it("should filter by manufacturers", () => {
    const result = filterByMultiSelect(testDiscs, { manufacturers: ["Innova", "Discraft"] });
    expect(result).toHaveLength(3);
  });

  it("should filter by conditions", () => {
    const result = filterByMultiSelect(testDiscs, { conditions: [DiscCondition.NEW] });
    expect(result).toHaveLength(1);
    expect(result[0].condition).toBe(DiscCondition.NEW);
  });

  it("should filter by locations", () => {
    const result = filterByMultiSelect(testDiscs, { locations: [Location.BAG, Location.HOME] });
    expect(result).toHaveLength(2);
  });

  it("should combine multiple multi-select filters", () => {
    const result = filterByMultiSelect(testDiscs, {
      manufacturers: ["Innova"],
      conditions: [DiscCondition.NEW, DiscCondition.BEAT_IN],
    });
    expect(result).toHaveLength(2);
  });
});

// ============================================================================
// COMPREHENSIVE FILTERING TESTS
// ============================================================================

describe("applyAllFilters", () => {
  it("should apply text search", () => {
    const criteria: EnhancedFilterCriteria = { searchTerm: "Destroyer" };
    const result = applyAllFilters(testDiscs, criteria);
    expect(result.discs).toHaveLength(1);
    expect(result.metrics.filtersApplied).toContain("textSearch");
  });

  it("should apply multiple filter types", () => {
    const criteria: EnhancedFilterCriteria = {
      manufacturers: ["Innova"],
      speedRange: { min: 10, max: 15 },
    };
    const result = applyAllFilters(testDiscs, criteria);
    expect(result.discs).toHaveLength(1);
    expect(result.metrics.filtersApplied).toContain("multiSelect");
    expect(result.metrics.filtersApplied).toContain("flightRanges");
  });

  it("should track performance metrics", () => {
    const criteria: EnhancedFilterCriteria = { searchTerm: "test" };
    const result = applyAllFilters(testDiscs, criteria);
    expect(result.metrics.totalDiscs).toBe(testDiscs.length);
    expect(result.metrics.executionTime).toBeGreaterThan(0);
  });
});

// ============================================================================
// UTILITY FUNCTION TESTS
// ============================================================================

describe("getUniqueFieldValues", () => {
  it("should extract unique manufacturers", () => {
    const result = getUniqueFieldValues(testDiscs, "manufacturer");
    expect(result).toEqual(["Innova", "Discraft", "Dynamic Discs"]);
  });

  it("should extract unique colors", () => {
    const result = getUniqueFieldValues(testDiscs, "color");
    expect(result).toEqual(["Blue", "Red", "Green", "Orange"]);
  });
});

describe("getFilterSuggestions", () => {
  it("should provide filter suggestions for non-empty collection", () => {
    const suggestions = getFilterSuggestions(testDiscs);
    expect(suggestions.manufacturers).toContain("Innova");
    expect(suggestions.weightRange.min).toBe(173);
    expect(suggestions.weightRange.max).toBe(180);
  });

  it("should provide default suggestions for empty collection", () => {
    const suggestions = getFilterSuggestions([]);
    expect(suggestions.manufacturers).toEqual([]);
    expect(suggestions.weightRange).toEqual({ min: 150, max: 180 });
  });
});

describe("hasActiveFilters", () => {
  it("should return false for empty criteria", () => {
    const criteria = createEmptyFilterCriteria();
    expect(hasActiveFilters(criteria)).toBe(false);
  });

  it("should return true for search term", () => {
    const criteria: EnhancedFilterCriteria = { searchTerm: "test" };
    expect(hasActiveFilters(criteria)).toBe(true);
  });

  it("should return true for filter arrays", () => {
    const criteria: EnhancedFilterCriteria = { manufacturers: ["Innova"] };
    expect(hasActiveFilters(criteria)).toBe(true);
  });
});

// ============================================================================
// URL SERIALIZATION TESTS
// ============================================================================

describe("URL serialization", () => {
  it("should serialize filters to URL params", () => {
    const criteria: EnhancedFilterCriteria = {
      searchTerm: "test",
      manufacturers: ["Innova", "Discraft"],
      weightRange: { min: 170, max: 180 },
    };
    const params = serializeFiltersToURL(criteria);
    expect(params.get("q")).toBe("test");
    expect(params.get("manufacturers")).toBe("Innova,Discraft");
    expect(params.get("weightMin")).toBe("170");
    expect(params.get("weightMax")).toBe("180");
  });

  it("should deserialize filters from URL params", () => {
    const params = new URLSearchParams({
      q: "test",
      manufacturers: "Innova,Discraft",
      weightMin: "170",
      weightMax: "180",
    });
    const criteria = deserializeFiltersFromURL(params);
    expect(criteria.searchTerm).toBe("test");
    expect(criteria.manufacturers).toEqual(["Innova", "Discraft"]);
    expect(criteria.weightRange).toEqual({ min: 170, max: 180 });
  });

  it("should handle round-trip serialization", () => {
    const original: EnhancedFilterCriteria = {
      searchTerm: "destroyer",
      manufacturers: ["Innova"],
      conditions: [DiscCondition.NEW],
      speedRange: { min: 10, max: 15 },
    };
    const params = serializeFiltersToURL(original);
    const deserialized = deserializeFiltersFromURL(params);

    expect(deserialized.searchTerm).toBe(original.searchTerm);
    expect(deserialized.manufacturers).toEqual(original.manufacturers);
    expect(deserialized.conditions).toEqual(original.conditions);
    expect(deserialized.speedRange).toEqual(original.speedRange);
  });
});
