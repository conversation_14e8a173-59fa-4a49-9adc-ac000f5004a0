/**
 * Disc Golf Data Constants for the Disc Golf Inventory Management System
 *
 * This file contains common disc golf manufacturers, plastic types, and other
 * data constants used for autocomplete suggestions and validation.
 */

// ============================================================================
// MANUFACTURER DATA
// ============================================================================

/**
 * Common disc golf manufacturers for autocomplete suggestions
 * Ordered by popularity and market presence
 */
export const DISC_MANUFACTURERS = [
  // Major Manufacturers
  "Innova",
  "Discraft",
  "Dynamic Discs",
  "Latitude 64",
  "Westside Discs",
  "MVP Disc Sports",
  "Axiom Discs",
  "Prodigy Disc",
  "Kastaplast",
  "Discmania",
  
  // Mid-tier Manufacturers
  "Gateway Disc Sports",
  "Millennium",
  "Legacy Discs",
  "Infinite Discs",
  "Streamline Discs",
  "Thought Space Athletics",
  "DGA",
  "Hyzer Bomb",
  "RPM Discs",
  "Lone Star Disc",
  
  // Specialty/Boutique Manufacturers
  "Mint Discs",
  "Clash Discs",
  "Daredevil Discs",
  "Yikun Discs",
  "Prodiscus",
  "Vibram",
  "Quest AT",
  "ABC Discs",
] as const;

// ============================================================================
// PLASTIC TYPE DATA
// ============================================================================

/**
 * Common plastic types organized by manufacturer
 * Used for autocomplete suggestions based on selected manufacturer
 */
export const PLASTIC_TYPES_BY_MANUFACTURER: Record<string, string[]> = {
  "Innova": [
    "Champion",
    "Star",
    "DX",
    "Pro",
    "GStar",
    "XT",
    "Blizzard Champion",
    "Halo Star",
    "Metal Flake Champion",
    "Nexus",
    "R-Pro",
  ],
  
  "Discraft": [
    "ESP",
    "Z",
    "Big Z",
    "Pro-D",
    "Elite X",
    "Elite Z",
    "Titanium",
    "Cryztal",
    "Cryztal Z",
    "Jawbreaker",
    "Rubber Blend",
    "Putter Line",
  ],
  
  "Dynamic Discs": [
    "Lucid",
    "Fuzion",
    "BioFuzion",
    "Prime",
    "Classic",
    "Fluid",
    "Lucid-X",
    "Moonshine",
    "Hybrid",
    "Supreme",
  ],
  
  "Latitude 64": [
    "Opto",
    "Gold",
    "Retro",
    "Zero",
    "Grand",
    "Frost",
    "Recycled",
    "Opto-X",
    "Royal",
    "Moonshine",
  ],
  
  "Westside Discs": [
    "VIP",
    "Tournament",
    "Origio",
    "Elasto",
    "VIP-X",
    "Moonshine",
  ],
  
  "MVP Disc Sports": [
    "Neutron",
    "Proton",
    "Plasma",
    "Electron",
    "Cosmic Neutron",
    "Eclipse",
    "Fission",
  ],
  
  "Axiom Discs": [
    "Neutron",
    "Proton",
    "Plasma",
    "Electron",
    "Cosmic Neutron",
    "Eclipse",
    "Fission",
  ],
  
  "Prodigy Disc": [
    "400",
    "400G",
    "500",
    "750",
    "750G",
    "300",
    "300 Soft",
    "Air",
    "Duraflex",
  ],
  
  "Kastaplast": [
    "K1",
    "K2",
    "K3",
    "K1 Soft",
    "K1 Glow",
    "K3 Glow",
  ],
  
  "Discmania": [
    "C-Line",
    "S-Line",
    "P-Line",
    "G-Line",
    "D-Line",
    "Lux",
    "Neo",
    "Evolution",
    "Active",
    "Flex",
  ],
};

/**
 * Generic plastic types for manufacturers not in the specific list
 */
export const GENERIC_PLASTIC_TYPES = [
  "Premium",
  "Base",
  "Mid-Grade",
  "Soft",
  "Glow",
  "Translucent",
  "Opaque",
  "Flexible",
  "Durable",
  "Grippy",
] as const;

// ============================================================================
// COMMON DISC COLORS
// ============================================================================

/**
 * Common disc colors for autocomplete suggestions
 */
export const DISC_COLORS = [
  "Red",
  "Blue",
  "Yellow",
  "Green",
  "Orange",
  "Purple",
  "Pink",
  "White",
  "Black",
  "Clear",
  "Translucent",
  "Glow",
  "Multi-Color",
  "Tie-Dye",
  "Swirl",
  "Marble",
  "Metallic",
  "Pearlescent",
] as const;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get plastic types for a specific manufacturer
 * @param manufacturer - The manufacturer name
 * @returns Array of plastic types for that manufacturer
 */
export function getPlasticTypesForManufacturer(manufacturer: string): string[] {
  return PLASTIC_TYPES_BY_MANUFACTURER[manufacturer] || GENERIC_PLASTIC_TYPES;
}

/**
 * Get all unique plastic types across all manufacturers
 * @returns Array of all plastic types
 */
export function getAllPlasticTypes(): string[] {
  const allTypes = new Set<string>();
  
  Object.values(PLASTIC_TYPES_BY_MANUFACTURER).forEach(types => {
    types.forEach(type => allTypes.add(type));
  });
  
  GENERIC_PLASTIC_TYPES.forEach(type => allTypes.add(type));
  
  return Array.from(allTypes).sort();
}

/**
 * Search manufacturers by partial name match
 * @param query - Search query
 * @returns Filtered array of manufacturers
 */
export function searchManufacturers(query: string): string[] {
  if (!query.trim()) return DISC_MANUFACTURERS.slice();
  
  const searchTerm = query.toLowerCase();
  return DISC_MANUFACTURERS.filter(manufacturer => 
    manufacturer.toLowerCase().includes(searchTerm)
  );
}

/**
 * Search plastic types by partial name match for a specific manufacturer
 * @param manufacturer - The manufacturer name
 * @param query - Search query
 * @returns Filtered array of plastic types
 */
export function searchPlasticTypes(manufacturer: string, query: string): string[] {
  const plasticTypes = getPlasticTypesForManufacturer(manufacturer);
  
  if (!query.trim()) return plasticTypes;
  
  const searchTerm = query.toLowerCase();
  return plasticTypes.filter(type => 
    type.toLowerCase().includes(searchTerm)
  );
}
