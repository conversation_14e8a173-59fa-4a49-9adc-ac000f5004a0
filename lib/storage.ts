/**
 * LocalStorage Service for the Disc Golf Inventory Management System
 *
 * This service provides type-safe localStorage operations with comprehensive error handling,
 * JSON serialization/deserialization, and quota management for disc golf inventory data.
 */

// ============================================================================
// ERROR TYPES & INTERFACES
// ============================================================================

/**
 * Storage-specific error types
 */
export enum StorageErrorType {
  QUOTA_EXCEEDED = "quota_exceeded",
  SERIALIZATION_ERROR = "serialization_error",
  DESERIALIZATION_ERROR = "deserialization_error",
  STORAGE_UNAVAILABLE = "storage_unavailable",
  INVALID_KEY = "invalid_key",
  UNKNOWN_ERROR = "unknown_error",
}

/**
 * Storage operation error
 */
export class StorageError extends Error {
  constructor(public type: StorageErrorType, message: string, public originalError?: Error) {
    super(message);
    this.name = "StorageError";
  }
}

/**
 * Result type for storage operations
 */
export interface StorageResult<T> {
  success: boolean;
  data?: T;
  error?: StorageError;
}

/**
 * Storage operation options
 */
export interface StorageOptions {
  /** Whether to validate data before storing */
  validate?: boolean;
  /** Custom serializer function */
  serializer?: (value: any) => string;
  /** Custom deserializer function */
  deserializer?: (value: string) => any;
}

// ============================================================================
// CORE STORAGE SERVICE
// ============================================================================

/**
 * Type-safe localStorage wrapper with error handling and JSON serialization
 */
export class LocalStorageService {
  private static instance: LocalStorageService;
  private isAvailable: boolean;

  private constructor() {
    this.isAvailable = this.checkStorageAvailability();
  }

  /**
   * Get singleton instance of LocalStorageService
   */
  public static getInstance(): LocalStorageService {
    if (!LocalStorageService.instance) {
      LocalStorageService.instance = new LocalStorageService();
    }
    return LocalStorageService.instance;
  }

  /**
   * Check if localStorage is available and functional
   */
  private checkStorageAvailability(): boolean {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return false;
      }

      // Test storage functionality
      const testKey = "__storage_test__";
      const testValue = "test";
      localStorage.setItem(testKey, testValue);
      const retrieved = localStorage.getItem(testKey);
      localStorage.removeItem(testKey);

      return retrieved === testValue;
    } catch {
      return false;
    }
  }

  /**
   * Validate storage key
   */
  private validateKey(key: string): void {
    if (!key || typeof key !== "string" || key.trim().length === 0) {
      throw new StorageError(StorageErrorType.INVALID_KEY, "Storage key must be a non-empty string");
    }
  }

  /**
   * Safe JSON serialization with error handling
   */
  private serialize(value: any, customSerializer?: (value: any) => string): string {
    try {
      if (customSerializer) {
        return customSerializer(value);
      }

      // Handle Date objects specially
      return JSON.stringify(value, (key, val) => {
        if (val instanceof Date) {
          return { __type: "Date", value: val.toISOString() };
        }
        return val;
      });
    } catch (error) {
      throw new StorageError(
        StorageErrorType.SERIALIZATION_ERROR,
        `Failed to serialize data: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Safe JSON deserialization with error handling
   */
  private deserialize<T>(value: string, customDeserializer?: (value: string) => any): T {
    try {
      if (customDeserializer) {
        return customDeserializer(value);
      }

      // Parse with Date object restoration
      return JSON.parse(value, (key, val) => {
        if (val && typeof val === "object" && val.__type === "Date") {
          return new Date(val.value);
        }
        return val;
      });
    } catch (error) {
      throw new StorageError(
        StorageErrorType.DESERIALIZATION_ERROR,
        `Failed to deserialize data: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Store data in localStorage with type safety and error handling
   */
  public setItem<T>(key: string, value: T, options: StorageOptions = {}): StorageResult<void> {
    try {
      this.validateKey(key);

      if (!this.isAvailable) {
        throw new StorageError(
          StorageErrorType.STORAGE_UNAVAILABLE,
          "localStorage is not available in this environment"
        );
      }

      const serializedValue = this.serialize(value, options.serializer);
      localStorage.setItem(key, serializedValue);

      return { success: true };
    } catch (error) {
      if (error instanceof StorageError) {
        return { success: false, error };
      }

      // Handle quota exceeded error
      if (error instanceof DOMException && error.name === "QuotaExceededError") {
        const storageError = new StorageError(
          StorageErrorType.QUOTA_EXCEEDED,
          "localStorage quota exceeded. Please free up space by removing some data.",
          error
        );
        return { success: false, error: storageError };
      }

      // Handle unknown errors
      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Unexpected error storing data: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      return { success: false, error: storageError };
    }
  }

  /**
   * Retrieve data from localStorage with type safety and error handling
   */
  public getItem<T>(key: string, options: StorageOptions = {}): StorageResult<T | null> {
    try {
      this.validateKey(key);

      if (!this.isAvailable) {
        throw new StorageError(
          StorageErrorType.STORAGE_UNAVAILABLE,
          "localStorage is not available in this environment"
        );
      }

      const item = localStorage.getItem(key);
      if (item === null) {
        return { success: true, data: null };
      }

      const deserializedValue = this.deserialize<T>(item, options.deserializer);
      return { success: true, data: deserializedValue };
    } catch (error) {
      if (error instanceof StorageError) {
        return { success: false, error };
      }

      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Unexpected error retrieving data: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      return { success: false, error: storageError };
    }
  }

  /**
   * Remove item from localStorage
   */
  public removeItem(key: string): StorageResult<void> {
    try {
      this.validateKey(key);

      if (!this.isAvailable) {
        throw new StorageError(
          StorageErrorType.STORAGE_UNAVAILABLE,
          "localStorage is not available in this environment"
        );
      }

      localStorage.removeItem(key);
      return { success: true };
    } catch (error) {
      if (error instanceof StorageError) {
        return { success: false, error };
      }

      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Unexpected error removing data: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      return { success: false, error: storageError };
    }
  }

  /**
   * Clear all data from localStorage
   */
  public clear(): StorageResult<void> {
    try {
      if (!this.isAvailable) {
        throw new StorageError(
          StorageErrorType.STORAGE_UNAVAILABLE,
          "localStorage is not available in this environment"
        );
      }

      localStorage.clear();
      return { success: true };
    } catch (error) {
      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Unexpected error clearing storage: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      return { success: false, error: storageError };
    }
  }

  /**
   * Get all keys from localStorage
   */
  public getAllKeys(): StorageResult<string[]> {
    try {
      if (!this.isAvailable) {
        throw new StorageError(
          StorageErrorType.STORAGE_UNAVAILABLE,
          "localStorage is not available in this environment"
        );
      }

      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          keys.push(key);
        }
      }

      return { success: true, data: keys };
    } catch (error) {
      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Unexpected error getting keys: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      return { success: false, error: storageError };
    }
  }

  /**
   * Check if localStorage is available
   */
  public isStorageAvailable(): boolean {
    return this.isAvailable;
  }

  /**
   * Get storage usage information
   */
  public getStorageInfo(): StorageResult<{ used: number; available: number; total: number }> {
    try {
      if (!this.isAvailable) {
        throw new StorageError(
          StorageErrorType.STORAGE_UNAVAILABLE,
          "localStorage is not available in this environment"
        );
      }

      // Calculate used storage
      let used = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          const value = localStorage.getItem(key);
          if (value) {
            used += key.length + value.length;
          }
        }
      }

      // Estimate total available storage (typically 5-10MB)
      const total = 5 * 1024 * 1024; // 5MB estimate
      const available = total - used;

      return {
        success: true,
        data: { used, available, total },
      };
    } catch (error) {
      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Unexpected error getting storage info: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      return { success: false, error: storageError };
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS & EXPORTS
// ============================================================================

/**
 * Get the singleton instance of LocalStorageService
 */
export const storage = LocalStorageService.getInstance();

/**
 * Convenience function to store data with error handling
 */
export function setStorageItem<T>(key: string, value: T, options?: StorageOptions): StorageResult<void> {
  return storage.setItem(key, value, options);
}

/**
 * Convenience function to retrieve data with error handling
 */
export function getStorageItem<T>(key: string, options?: StorageOptions): StorageResult<T | null> {
  return storage.getItem<T>(key, options);
}

/**
 * Convenience function to remove data with error handling
 */
export function removeStorageItem(key: string): StorageResult<void> {
  return storage.removeItem(key);
}

/**
 * Convenience function to clear all storage with error handling
 */
export function clearStorage(): StorageResult<void> {
  return storage.clear();
}

/**
 * Check if localStorage is available in the current environment
 */
export function isStorageAvailable(): boolean {
  return storage.isStorageAvailable();
}

/**
 * Get storage usage information
 */
export function getStorageInfo(): StorageResult<{ used: number; available: number; total: number }> {
  return storage.getStorageInfo();
}

// ============================================================================
// DISC-SPECIFIC STORAGE UTILITIES
// ============================================================================

import type { Disc } from "./types";

/**
 * Storage keys for disc golf inventory data
 */
export const STORAGE_KEYS = {
  DISCS: "disc_golf_inventory_discs",
  USER_PREFERENCES: "disc_golf_inventory_preferences",
  SEARCH_HISTORY: "disc_golf_inventory_search_history",
  FILTERS: "disc_golf_inventory_filters",
} as const;

/**
 * Store disc collection with validation
 */
export function storeDiscCollection(discs: Disc[]): StorageResult<void> {
  return setStorageItem(STORAGE_KEYS.DISCS, discs);
}

/**
 * Retrieve disc collection with type safety
 */
export function getDiscCollection(): StorageResult<Disc[] | null> {
  return getStorageItem<Disc[]>(STORAGE_KEYS.DISCS);
}

/**
 * Store user preferences
 */
export function storeUserPreferences(preferences: Record<string, any>): StorageResult<void> {
  return setStorageItem(STORAGE_KEYS.USER_PREFERENCES, preferences);
}

/**
 * Retrieve user preferences
 */
export function getUserPreferences(): StorageResult<Record<string, any> | null> {
  return getStorageItem<Record<string, any>>(STORAGE_KEYS.USER_PREFERENCES);
}

/**
 * Default export for the storage service
 */
export default storage;
