/**
 * Form Components Usage Examples
 * 
 * This file demonstrates various ways to use the FormField and AddDiscForm components
 * in different scenarios and configurations.
 */

import * as React from "react";
import { AddDiscForm, FormField, FormFieldGroup } from "@/components/forms";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useInventory } from "@/hooks/useInventory";
import type { CreateDiscInput } from "@/hooks/useInventory";

// ============================================================================
// EXAMPLE 1: Basic AddDiscForm Usage
// ============================================================================

export function BasicAddDiscFormExample() {
  const handleSuccess = (disc: CreateDiscInput) => {
    console.log("Disc added successfully:", disc);
    // Handle success - could redirect, show notification, etc.
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Add New Disc</h2>
      <AddDiscForm onSuccess={handleSuccess} />
    </div>
  );
}

// ============================================================================
// EXAMPLE 2: AddDiscForm in Modal/Dialog
// ============================================================================

export function ModalAddDiscFormExample() {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleSuccess = (disc: CreateDiscInput) => {
    console.log("Disc added:", disc);
    setIsOpen(false); // Close modal on success
  };

  const handleCancel = () => {
    setIsOpen(false);
  };

  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>Add Disc</Button>
      
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <AddDiscForm
              onSuccess={handleSuccess}
              onCancel={handleCancel}
              showCard={false}
              className="p-6"
            />
          </div>
        </div>
      )}
    </div>
  );
}

// ============================================================================
// EXAMPLE 3: AddDiscForm without Card Wrapper
// ============================================================================

export function InlineAddDiscFormExample() {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Disc Collection Manager</h1>
        <p className="text-muted-foreground">Add a new disc to your collection</p>
      </div>
      
      <AddDiscForm 
        showCard={false}
        onSuccess={(disc) => {
          // Could navigate to disc detail page
          window.location.href = `/discs/${disc.id}`;
        }}
      />
    </div>
  );
}

// ============================================================================
// EXAMPLE 4: Custom Form with FormField Components
// ============================================================================

export function CustomFormExample() {
  const [formData, setFormData] = React.useState({
    name: "",
    email: "",
    phone: "",
    notes: "",
  });
  
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  const handleChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    // Simple validation
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    console.log("Form submitted:", formData);
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Contact Information</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <FormFieldGroup
            title="Personal Information"
            description="Please provide your contact details"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                id="name"
                label="Full Name"
                placeholder="John Doe"
                required
                value={formData.name}
                onChange={handleChange("name")}
                error={errors.name}
              />
              
              <FormField
                id="email"
                label="Email Address"
                type="email"
                placeholder="<EMAIL>"
                required
                value={formData.email}
                onChange={handleChange("email")}
                error={errors.email}
              />
            </div>
            
            <FormField
              id="phone"
              label="Phone Number"
              type="tel"
              placeholder="+****************"
              value={formData.phone}
              onChange={handleChange("phone")}
              helpText="Optional - for urgent contact only"
            />
            
            <FormField
              id="notes"
              label="Additional Notes"
              placeholder="Any additional information..."
              value={formData.notes}
              onChange={handleChange("notes")}
            />
          </FormFieldGroup>
          
          <div className="flex gap-3">
            <Button type="submit">Submit</Button>
            <Button 
              type="button" 
              variant="outline"
              onClick={() => {
                setFormData({ name: "", email: "", phone: "", notes: "" });
                setErrors({});
              }}
            >
              Reset
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

// ============================================================================
// EXAMPLE 5: AddDiscForm with Custom Success Handling
// ============================================================================

export function AddDiscWithNotificationExample() {
  const [notification, setNotification] = React.useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);

  const { discs } = useInventory();

  const handleSuccess = (disc: CreateDiscInput) => {
    setNotification({
      type: "success",
      message: `Successfully added ${disc.manufacturer} ${disc.mold} to your collection!`
    });
    
    // Auto-hide notification after 5 seconds
    setTimeout(() => setNotification(null), 5000);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`p-4 rounded-md ${
          notification.type === "success" 
            ? "bg-green-100 text-green-800 border border-green-200" 
            : "bg-red-100 text-red-800 border border-red-200"
        }`}>
          {notification.message}
        </div>
      )}
      
      {/* Collection Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Collection Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-lg">
            You currently have <strong>{discs.length}</strong> discs in your collection.
          </p>
        </CardContent>
      </Card>
      
      {/* Add Disc Form */}
      <AddDiscForm onSuccess={handleSuccess} />
    </div>
  );
}

// ============================================================================
// EXAMPLE 6: FormField Variants Showcase
// ============================================================================

export function FormFieldVariantsExample() {
  const [values, setValues] = React.useState({
    text: "",
    email: "",
    password: "",
    number: 0,
    date: "",
    url: "",
  });

  const handleChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setValues(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>FormField Variants</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormField
          id="text-field"
          label="Text Field"
          placeholder="Enter text"
          value={values.text}
          onChange={handleChange("text")}
          helpText="This is a standard text input"
        />
        
        <FormField
          id="email-field"
          label="Email Field"
          type="email"
          placeholder="<EMAIL>"
          required
          value={values.email}
          onChange={handleChange("email")}
        />
        
        <FormField
          id="password-field"
          label="Password Field"
          type="password"
          placeholder="Enter password"
          required
          value={values.password}
          onChange={handleChange("password")}
          helpText="Password must be at least 8 characters"
        />
        
        <FormField
          id="number-field"
          label="Number Field"
          type="number"
          placeholder="123"
          value={values.number}
          onChange={(e) => setValues(prev => ({ ...prev, number: parseInt(e.target.value) || 0 }))}
          inputProps={{ min: 0, max: 100, step: 1 }}
        />
        
        <FormField
          id="date-field"
          label="Date Field"
          type="date"
          value={values.date}
          onChange={handleChange("date")}
        />
        
        <FormField
          id="url-field"
          label="URL Field"
          type="url"
          placeholder="https://example.com"
          value={values.url}
          onChange={handleChange("url")}
        />
        
        <FormField
          id="disabled-field"
          label="Disabled Field"
          placeholder="This field is disabled"
          disabled
          helpText="This field cannot be edited"
        />
        
        <FormField
          id="error-field"
          label="Field with Error"
          placeholder="This field has an error"
          error="This is an example error message"
        />
      </CardContent>
    </Card>
  );
}
