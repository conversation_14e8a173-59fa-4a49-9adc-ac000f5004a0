/**
 * Search & Filter Usage Examples
 * 
 * This file demonstrates practical usage of the search and filter infrastructure
 * including the useSearch hook and filter utilities.
 */

"use client";

import React, { useState } from 'react';
import { useSearch } from '@/hooks/useSearch';
import { useInventory } from '@/hooks/useInventory';
import { DiscCondition, Location } from '@/lib/types';
import type { EnhancedFilterCriteria } from '@/lib/filterUtils';

// ============================================================================
// BASIC SEARCH COMPONENT
// ============================================================================

export function BasicSearchExample() {
  const { discs } = useInventory();
  const {
    searchState,
    filteredDiscs,
    setQuery,
    clearSearch,
    hasActiveSearch
  } = useSearch(discs);

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <input
          type="text"
          value={searchState.query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search discs..."
          className="flex-1 px-3 py-2 border rounded-md"
        />
        {hasActiveSearch && (
          <button
            onClick={clearSearch}
            className="px-3 py-2 bg-gray-500 text-white rounded-md"
          >
            Clear
          </button>
        )}
      </div>
      
      <div className="text-sm text-gray-600">
        Found {filteredDiscs.length} of {discs.length} discs
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredDiscs.map((disc) => (
          <div key={disc.id} className="p-4 border rounded-md">
            <h3 className="font-semibold">{disc.manufacturer} {disc.mold}</h3>
            <p className="text-sm text-gray-600">{disc.plasticType} • {disc.weight}g</p>
            <p className="text-sm">{disc.color}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

// ============================================================================
// ADVANCED FILTER COMPONENT
// ============================================================================

export function AdvancedFilterExample() {
  const { discs } = useInventory();
  const {
    searchState,
    filteredDiscs,
    setQuery,
    setFilters,
    clearAll,
    hasActiveSearch,
    hasActiveFilters,
    filterSuggestions
  } = useSearch(discs, {
    debounceDelay: 300,
    syncWithURL: true,
    enableMetrics: true
  });

  const handleFilterChange = (newFilters: Partial<EnhancedFilterCriteria>) => {
    setFilters({ ...searchState.filters, ...newFilters });
  };

  return (
    <div className="space-y-6">
      {/* Search Input */}
      <div className="flex gap-2">
        <input
          type="text"
          value={searchState.query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search discs..."
          className="flex-1 px-3 py-2 border rounded-md"
        />
        {(hasActiveSearch || hasActiveFilters) && (
          <button
            onClick={clearAll}
            className="px-3 py-2 bg-red-500 text-white rounded-md"
          >
            Clear All
          </button>
        )}
      </div>

      {/* Filter Panel */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-md">
        {/* Manufacturer Filter */}
        <div>
          <label className="block text-sm font-medium mb-2">Manufacturers</label>
          <select
            multiple
            value={searchState.filters.manufacturers || []}
            onChange={(e) => {
              const selected = Array.from(e.target.selectedOptions, option => option.value);
              handleFilterChange({ manufacturers: selected.length ? selected : undefined });
            }}
            className="w-full px-3 py-2 border rounded-md"
            size={4}
          >
            {filterSuggestions.manufacturers.map((manufacturer) => (
              <option key={manufacturer} value={manufacturer}>
                {manufacturer}
              </option>
            ))}
          </select>
        </div>

        {/* Condition Filter */}
        <div>
          <label className="block text-sm font-medium mb-2">Conditions</label>
          <div className="space-y-1">
            {Object.values(DiscCondition).map((condition) => (
              <label key={condition} className="flex items-center">
                <input
                  type="checkbox"
                  checked={searchState.filters.conditions?.includes(condition) || false}
                  onChange={(e) => {
                    const current = searchState.filters.conditions || [];
                    const updated = e.target.checked
                      ? [...current, condition]
                      : current.filter(c => c !== condition);
                    handleFilterChange({ conditions: updated.length ? updated : undefined });
                  }}
                  className="mr-2"
                />
                <span className="text-sm capitalize">{condition.replace('_', ' ')}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Speed Range Filter */}
        <div>
          <label className="block text-sm font-medium mb-2">Speed Range</label>
          <div className="space-y-2">
            <input
              type="range"
              min={filterSuggestions.speedRange.min}
              max={filterSuggestions.speedRange.max}
              value={searchState.filters.speedRange?.min || filterSuggestions.speedRange.min}
              onChange={(e) => {
                const min = parseInt(e.target.value);
                const max = searchState.filters.speedRange?.max || filterSuggestions.speedRange.max;
                handleFilterChange({ speedRange: { min, max } });
              }}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-600">
              <span>Min: {searchState.filters.speedRange?.min || filterSuggestions.speedRange.min}</span>
              <span>Max: {searchState.filters.speedRange?.max || filterSuggestions.speedRange.max}</span>
            </div>
          </div>
        </div>

        {/* Location Filter */}
        <div>
          <label className="block text-sm font-medium mb-2">Locations</label>
          <div className="space-y-1">
            {Object.values(Location).map((location) => (
              <label key={location} className="flex items-center">
                <input
                  type="checkbox"
                  checked={searchState.filters.locations?.includes(location) || false}
                  onChange={(e) => {
                    const current = searchState.filters.locations || [];
                    const updated = e.target.checked
                      ? [...current, location]
                      : current.filter(l => l !== location);
                    handleFilterChange({ locations: updated.length ? updated : undefined });
                  }}
                  className="mr-2"
                />
                <span className="text-sm capitalize">{location}</span>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Results and Metrics */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          Found {filteredDiscs.length} of {discs.length} discs
        </div>
        {searchState.metrics && (
          <div className="text-xs text-gray-500">
            Search took {searchState.metrics.executionTime.toFixed(2)}ms
            {searchState.metrics.filtersApplied.length > 0 && (
              <span> • Filters: {searchState.metrics.filtersApplied.join(', ')}</span>
            )}
          </div>
        )}
      </div>

      {/* Results Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredDiscs.map((disc) => (
          <div key={disc.id} className="p-4 border rounded-md hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-semibold">{disc.manufacturer} {disc.mold}</h3>
              <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                {disc.condition.replace('_', ' ')}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-1">
              {disc.plasticType} • {disc.weight}g • {disc.color}
            </p>
            <p className="text-xs text-gray-500 mb-2">
              Speed: {disc.flightNumbers.speed} | Glide: {disc.flightNumbers.glide} | 
              Turn: {disc.flightNumbers.turn} | Fade: {disc.flightNumbers.fade}
            </p>
            <div className="flex justify-between items-center">
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                {disc.currentLocation}
              </span>
              {disc.notes && (
                <span className="text-xs text-gray-400" title={disc.notes}>
                  📝 Notes
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredDiscs.length === 0 && discs.length > 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No discs match your search criteria.</p>
          <button
            onClick={clearAll}
            className="mt-2 text-blue-500 hover:text-blue-700"
          >
            Clear all filters
          </button>
        </div>
      )}
    </div>
  );
}

// ============================================================================
// URL SYNCHRONIZATION EXAMPLE
// ============================================================================

export function URLSyncExample() {
  const { discs } = useInventory();
  const searchHook = useSearch(discs, {
    syncWithURL: true,
    immediateURLUpdate: false
  });

  const [urlDisplay, setUrlDisplay] = useState('');

  // Update URL display when search state changes
  React.useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    setUrlDisplay(window.location.pathname + (params.toString() ? '?' + params.toString() : ''));
  }, [searchHook.searchState]);

  return (
    <div className="space-y-4">
      <div className="p-4 bg-blue-50 rounded-md">
        <h3 className="font-semibold mb-2">URL Synchronization Demo</h3>
        <p className="text-sm text-gray-600 mb-2">
          Search and filter changes are automatically synced to the URL for shareable links.
        </p>
        <div className="text-xs font-mono bg-white p-2 rounded border">
          {urlDisplay || '/inventory'}
        </div>
      </div>

      <BasicSearchExample />
    </div>
  );
}

// ============================================================================
// PERFORMANCE METRICS EXAMPLE
// ============================================================================

export function PerformanceMetricsExample() {
  const { discs } = useInventory();
  const {
    searchState,
    filteredDiscs,
    setQuery,
    setFilters
  } = useSearch(discs, {
    enableMetrics: true
  });

  return (
    <div className="space-y-4">
      <div className="p-4 bg-green-50 rounded-md">
        <h3 className="font-semibold mb-2">Performance Metrics</h3>
        {searchState.metrics ? (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Total Discs</div>
              <div className="text-gray-600">{searchState.metrics.totalDiscs}</div>
            </div>
            <div>
              <div className="font-medium">Filtered Discs</div>
              <div className="text-gray-600">{searchState.metrics.filteredDiscs}</div>
            </div>
            <div>
              <div className="font-medium">Execution Time</div>
              <div className="text-gray-600">{searchState.metrics.executionTime.toFixed(2)}ms</div>
            </div>
            <div>
              <div className="font-medium">Filters Applied</div>
              <div className="text-gray-600">
                {searchState.metrics.filtersApplied.length || 'None'}
              </div>
            </div>
          </div>
        ) : (
          <p className="text-gray-600">No metrics available</p>
        )}
      </div>

      <input
        type="text"
        value={searchState.query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Type to see performance metrics..."
        className="w-full px-3 py-2 border rounded-md"
      />

      <div className="text-sm text-gray-600">
        Results: {filteredDiscs.length} discs
      </div>
    </div>
  );
}
