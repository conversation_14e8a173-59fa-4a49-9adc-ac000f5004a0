---
type: "always_apply"
description: "At the start of EVERY task - this is not optional."
---

# AUGMENT.md

This file provides **strict and non-negotiable guidance** to AUGMENT Code when working with code in this repository. All
instructions and references herein are **mandatory** and supersede any internal assumptions.

## Project Overview

The Disc Golf Inventory Management System is a comprehensive personal disc golf inventory management system that
empowers players to optimize their game through better equipment knowledge and organization.

## Project Guidelines Reference (AUTHORITATIVE SOURCE)

This project follows comprehensive guidelines documented in the `docs/` folder. **These documents are the single source
of truth for all project decisions, standards, and methodologies.** Always reference these documents **first and
thoroughly** for any architectural decisions, coding standards, or project requirements.

- **docs/structure.md**: Project structure and architectural patterns
- **docs/tech.md**: Technology stack specification and tool versions
- **docs/rules.md**: **CRITICAL: Development standards and quality gates (Mandatory Adherence)**
- **docs/requirements.md**: Functional and non-functional requirements
- **docs/design.md**: Technical architecture and design decisions
- **docs/tasks.md**: Implementation task breakdown

**ABSOLUTELY CRITICAL**:

- **NEVER DEVIATE** from the standards, policies, and methodologies documented in `docs/rules.md`.
- **ZERO EXCEPTIONS** will be made for linting errors, type safety violations, test failures, or any form of technical
  debt.
- All code must reflect **immaculate attention to detail** and adhere to professional electrical design standards.

---

## CI/CD Quality Gates (PRIMARY ENFORCEMENT)

**CRITICAL**: The project uses **automated quality enforcement** through GitHub Actions CI/CD pipelines. Quality gates
run automatically on every Pull Request and prevent merging non-compliant code.

### Local Development Quality Checks

```bash
# Run comprehensive quality checks locally
./scripts/quality-check.sh

# Checks with auto-fix
./scripts/quality-check.sh --fix

# Checks with verbose output
./scripts/quality-check.sh --verbose
```

### Quality Gate Components

**CI/CD Quality Gates** (GitHub Actions):

- **Frontend Quality Gates**: Prettier formatting, ESLint linting, TypeScript compilation, Unit tests, Build
  verification
- **Branch Protection**: Prevents merging PRs with failing quality gates

**Development Workflow**:

1. Create feature branch and make changes
2. Run local quality checks: `./scripts/quality-check.sh`
3. Push branch and create Pull Request
4. GitHub Actions automatically run all quality gates
5. Fix any failing checks - PR cannot merge until all pass
6. Merge when all quality gates pass and PR is approved

### Emergency Override (Local Development Only)

```bash
# Bypass local checks (CI will still enforce)
git commit --no-verify -m "Emergency commit - will fix in PR"
```

## Manual Development Commands (FOR REFERENCE)

### App

```bash
# Run pnpm commands from app/
cd ~/dev/disc-golf-inventory/app/
# Start the app server
pnpm run dev

# Manual quality checks (automated via pre-commit hooks)
pnpm tsc --noEmit
pnpm next lint --fix
pnpm prettier --write --log-level=warn \"**/*.{ts,tsx,mdx}\" --cache

# Testing (MUST PASS WITH REQUIRED COVERAGE)
pnpm vitest [source] --run
pnpm vitest [source] --coverage --run
```

---

## Testing Strategy (MANDATORY ADHERENCE)

**All testing requirements, standards, and procedures are documented in the authoritative testing documentation:**

📋 **[TESTING.md](docs/TESTING.md)** - Complete Testing Strategy & Implementation Guide

This comprehensive document contains:

- **Testing Standards & Requirements** (coverage, pass rates, quality gates)
- **Development Commands** for app (pnpm)
- **App Testing Strategies** (architecture, patterns, best practices)
- **Testing Workflows** (5-phase methodology, CI/CD integration)
- **Infrastructure & Achievements** (historical context, resolved issues)

**Mandatory Requirements:**

- **Automated quality enforcement** via CI/CD pipelines (GitHub Actions)
- **100% pass rate** for all tests before commits
- **Coverage targets** as specified in TESTING.md
- **Zero tolerance** for test failures in main branch
- **Automated test logging** - dual logs track all test runs and failures
- **Zero manual quality verification** - automation prevents non-compliant commits

**Test Logging System:**

- **Automatic Logging**: All test runs generate logs in `app/test_logs/`
- **Summary Log**: `failed_tests.log` provides quick overview of failed tests
- **Verbose Log**: `failed_tests_verbose.log` contains detailed failure information
- **Console Output**: Dot-mode display for clean test execution without verbose spam
- **Scope Detection**: Automatically identifies test scope (specific files, markers, etc.)

---

## Development Standards (STRICTLY ENFORCED)

**Adherence to these standards is paramount and non-negotiable for every single line of code.**

1. **Robust design principles:** Apply **SOLID** principles for structural design, ensuring maintainability and
   flexibility through focused responsibilities, extensibility, and proper abstraction. Complement these with practices
   like **DRY**, **KISS**, and **TDD** to streamline implementation, reduce complexity, and enhance overall code
   quality. **No exceptions.**
2. **5-Phase Methodology:** Adopt a systematic 5-phase approach for each feature or task. This structured,
   quality-driven development process is **mandatory** for all work.
   1. **Discovery & Analysis:** Understand the current state of the system, identify requirements, and define the scope
      of the main task.
   2. **Task Planning:** Break down tasks into smaller, manageable units (max 30-minute work batches) to ensure
      efficient progress.
   3. **Implementation:** Execute changes with **engineering-grade quality**, focusing on unified patterns and
      professional electrical design standards.
   4. **Verification:** Ensure all requirements are met through comprehensive testing and **automated quality gate
      compliance** (pre-commit hooks prevent non-compliant code).
   5. **Documentation & Handover:** Prepare comprehensive documentation and create a handover package for future
      development and AI agent transfer.
3. **Unified Patterns:** Apply consistent "unified patterns" for calculations, service layers, and repositories. Utilize
   decorators for error handling, performance monitoring, and memory optimization as specified in `docs/design.md`.
   **Consistency is key.**
4. **Quality & Standards Focus:** Ensure **immaculate attention to detail**. Adhere to professional electrical design
   standards (IEC/EN). Maintain **complete type safety** with MyPy validation (100% compliance) and comprehensive
   testing (including real database connections where appropriate).
5. **Key Success Metrics:** Define success through high unified patterns compliance (≥90%), extensive test coverage
   (≥85% overall, 100% for critical logic), 100% test pass rates, and zero remaining placeholder implementations.
   **These are minimum success criteria.**

---

### App Module Dependencies

1. **App Router** → **Modules** → **Components** → **UI Primitives**
2. **State Management** (Zustand) for client state.
3. **React Query** for server state management.
4. **API Client** for backend communication.

---

## Performance Optimization

- **Frontend**: Implement React Query for server state caching, code splitting, and image optimization.

---
