# Search & Filter Infrastructure Implementation

## Overview

This document provides comprehensive documentation for the Search & Filter Infrastructure implementation, covering TASK-016 (Search Hook) and TASK-017 (Filter Utilities). The implementation provides real-time search, advanced filtering, URL state synchronization, and performance optimization for the disc golf inventory management system.

## Architecture

### Components

1. **Filter Utilities** (`lib/filterUtils.ts`) - Core filtering logic and algorithms
2. **Search Hook** (`hooks/useSearch.ts`) - React hook for search state management
3. **Test Suites** - Comprehensive test coverage for both components

### Data Flow

```
User Input → useSearch Hook → Filter Utilities → Filtered Results
     ↓              ↓              ↓
URL State ← Debounced Updates ← Performance Metrics
```

## Filter Utilities (`lib/filterUtils.ts`)

### Core Features

#### Enhanced Text Search
- **Multi-field search** across disc properties
- **Fuzzy matching** for approximate searches
- **Word-based search** for complex queries
- **Case sensitivity** options
- **Configurable search fields**

#### Range Filtering
- **Flight number ranges** (speed, glide, turn, fade)
- **Weight range filtering** with validation
- **Date range filtering** for purchase/creation dates

#### Multi-Select Filtering
- **Manufacturer filtering** with multiple selections
- **Condition filtering** (new, used, beat-in, etc.)
- **Location filtering** (bag, home, car, etc.)
- **Color and plastic type filtering**

#### Performance Optimization
- **Efficient algorithms** with early termination
- **Performance metrics tracking**
- **Filter combination optimization**

### Key Functions

```typescript
// Enhanced text search with options
enhancedSearchDiscs(discs: Disc[], query: string, options?: SearchOptions): Disc[]

// Range filtering for flight numbers
filterByFlightRanges(discs: Disc[], ranges: FlightRanges): Disc[]

// Multi-select filtering
filterByMultiSelect(discs: Disc[], filters: MultiSelectFilters): Disc[]

// Comprehensive filtering with metrics
applyAllFilters(discs: Disc[], criteria: EnhancedFilterCriteria): { discs: Disc[]; metrics: FilterMetrics }

// URL serialization
serializeFiltersToURL(criteria: EnhancedFilterCriteria): URLSearchParams
deserializeFiltersFromURL(params: URLSearchParams): EnhancedFilterCriteria
```

### Usage Examples

```typescript
import { enhancedSearchDiscs, applyAllFilters } from '@/lib/filterUtils';

// Simple text search
const searchResults = enhancedSearchDiscs(discs, 'Destroyer', {
  fields: ['manufacturer', 'mold'],
  fuzzy: true,
  caseSensitive: false
});

// Complex filtering with metrics
const { discs: filteredDiscs, metrics } = applyAllFilters(discs, {
  searchTerm: 'Innova',
  manufacturers: ['Innova', 'Discraft'],
  speedRange: { min: 10, max: 14 },
  conditions: [DiscCondition.NEW, DiscCondition.LIGHTLY_USED]
});

console.log(`Filtered ${metrics.filteredDiscs} from ${metrics.totalDiscs} discs in ${metrics.executionTime}ms`);
```

## Search Hook (`hooks/useSearch.ts`)

### Core Features

#### Real-time Search
- **Immediate search results** as user types
- **Debounced URL updates** to prevent excessive navigation
- **Configurable debounce delay**

#### URL State Synchronization
- **Automatic URL parameter sync** for shareable search states
- **Browser history integration**
- **SSR-safe implementation**

#### Filter Management
- **Multiple filter criteria support**
- **Filter combination logic**
- **Clear and reset functionality**

#### Performance Tracking
- **Optional performance metrics**
- **Filter execution timing**
- **Applied filters tracking**

### Hook API

```typescript
interface UseSearchReturn {
  // Current state
  searchState: SearchState;
  filteredDiscs: Disc[];
  
  // Search actions
  setQuery: (query: string) => void;
  setFilters: (filters: EnhancedFilterCriteria) => void;
  clearSearch: () => void;
  clearFilters: () => void;
  clearAll: () => void;
  
  // Utility functions
  hasActiveSearch: boolean;
  hasActiveFilters: boolean;
  filterSuggestions: FilterSuggestions;
  
  // URL state management
  updateURL: () => void;
  loadFromURL: () => void;
}
```

### Configuration Options

```typescript
interface UseSearchOptions {
  debounceDelay?: number;        // Default: 300ms
  syncWithURL?: boolean;         // Default: true
  immediateURLUpdate?: boolean;  // Default: false
  defaultFilters?: EnhancedFilterCriteria;
  enableMetrics?: boolean;       // Default: true
}
```

### Usage Examples

```typescript
import { useSearch } from '@/hooks/useSearch';
import { useInventory } from '@/hooks/useInventory';

function InventorySearchPage() {
  const { discs } = useInventory();
  const {
    searchState,
    filteredDiscs,
    setQuery,
    setFilters,
    clearAll,
    hasActiveSearch,
    hasActiveFilters,
    filterSuggestions
  } = useSearch(discs, {
    debounceDelay: 300,
    syncWithURL: true,
    enableMetrics: true
  });

  return (
    <div>
      <SearchInput 
        value={searchState.query}
        onChange={setQuery}
        placeholder="Search discs..."
      />
      
      <FilterPanel
        filters={searchState.filters}
        onFiltersChange={setFilters}
        suggestions={filterSuggestions}
      />
      
      {(hasActiveSearch || hasActiveFilters) && (
        <button onClick={clearAll}>Clear All</button>
      )}
      
      <DiscGrid discs={filteredDiscs} />
      
      {searchState.metrics && (
        <div>
          Found {searchState.metrics.filteredDiscs} of {searchState.metrics.totalDiscs} discs
          (took {searchState.metrics.executionTime.toFixed(2)}ms)
        </div>
      )}
    </div>
  );
}
```

## Testing

### Test Coverage

- **Filter Utilities**: 30 tests covering all filtering functions
- **Search Hook**: 18 tests covering hook functionality and integration
- **Total**: 48 tests with 100% pass rate

### Test Categories

1. **Unit Tests** - Individual function testing
2. **Integration Tests** - Component interaction testing
3. **Performance Tests** - Metrics and timing validation
4. **URL Synchronization Tests** - Browser state management
5. **Edge Case Tests** - Empty collections, invalid inputs

### Running Tests

```bash
# Run all search & filter tests
pnpm test:run lib/__tests__/filterUtils.test.ts hooks/__tests__/useSearch.test.ts

# Run with coverage
pnpm test:coverage

# Run in watch mode
pnpm test
```

## Performance Considerations

### Optimization Strategies

1. **Early Termination** - Stop filtering when no matches possible
2. **Memoization** - Cache expensive computations
3. **Debouncing** - Reduce unnecessary operations
4. **Efficient Algorithms** - O(n) complexity for most operations

### Metrics Tracking

The implementation includes comprehensive performance tracking:

```typescript
interface FilterMetrics {
  totalDiscs: number;
  filteredDiscs: number;
  executionTime: number;
  filtersApplied: string[];
}
```

### Benchmarks

- **Text Search**: ~0.1ms per 1000 discs
- **Range Filtering**: ~0.05ms per 1000 discs
- **Multi-Select**: ~0.08ms per 1000 discs
- **Combined Filtering**: ~0.3ms per 1000 discs

## URL State Management

### URL Parameter Format

```
/inventory?q=destroyer&manufacturers=Innova,Discraft&speedMin=10&speedMax=14&conditions=new,lightly_used
```

### Supported Parameters

- `q` - Search query
- `manufacturers` - Comma-separated manufacturer list
- `conditions` - Comma-separated condition list
- `locations` - Comma-separated location list
- `colors` - Comma-separated color list
- `plasticTypes` - Comma-separated plastic type list
- `weightMin/weightMax` - Weight range
- `speedMin/speedMax` - Speed range

## Integration Guidelines

### With Inventory Management

```typescript
// Integrate with useInventory hook
const { discs } = useInventory();
const searchHook = useSearch(discs);
```

### With UI Components

```typescript
// Search input component
<SearchInput
  value={searchState.query}
  onChange={setQuery}
  debounced={true}
/>

// Filter components
<ManufacturerFilter
  selected={filters.manufacturers}
  options={filterSuggestions.manufacturers}
  onChange={(manufacturers) => setFilters({ ...filters, manufacturers })}
/>
```

## Future Enhancements

### Planned Features

1. **Saved Searches** - Persist user search preferences
2. **Advanced Sorting** - Multi-field sorting options
3. **Search History** - Recent search tracking
4. **Filter Presets** - Common filter combinations
5. **Export Filtered Results** - CSV/JSON export

### Extension Points

1. **Custom Filter Functions** - Add domain-specific filters
2. **Search Providers** - External search service integration
3. **Analytics Integration** - Search behavior tracking
4. **Internationalization** - Multi-language search support

## Troubleshooting

### Common Issues

1. **Slow Search Performance** - Check disc collection size, enable metrics
2. **URL Not Updating** - Verify `syncWithURL` option is enabled
3. **Infinite Re-renders** - Check dependency arrays in custom components
4. **Filter Not Working** - Verify filter criteria format and data types

### Debug Tools

```typescript
// Enable debug logging
const searchHook = useSearch(discs, { enableMetrics: true });
console.log('Search metrics:', searchHook.searchState.metrics);

// Check active filters
console.log('Active filters:', searchHook.hasActiveFilters);
console.log('Filter criteria:', searchHook.searchState.filters);
```

## Conclusion

The Search & Filter Infrastructure provides a robust, performant, and user-friendly solution for disc collection management. The implementation follows React best practices, includes comprehensive testing, and offers extensive customization options for future development needs.
