# Disc Golf Inventory Management System Documentation

## 06 — Design

**Purpose:** This document serves as the technical blueprint for the Disc Golf Inventory Management System. It details the UX/UI principles, data schema, component architecture, and interaction patterns, ensuring a consistent, scalable, and high-quality user experience.

---

## User Experience (UX) & User Interface (UI) Principles

### Design System Foundation

**Design Philosophy**: Clean, modern, and functional design that prioritizes usability and accessibility while reflecting the outdoor, athletic nature of disc golf.

**Color Palette**:
- **Primary**: Emerald green (#10b981) - representing outdoor/nature theme
- **Secondary**: Slate blue (#475569) - for secondary actions and text
- **Accent**: Orange (#f97316) - for highlights and call-to-action elements
- **Neutral**: Gray scale (#f8fafc to #0f172a) - for backgrounds and text
- **Semantic**: Green (#22c55e) for success, Red (#ef4444) for errors, Yellow (#eab308) for warnings

**Typography**:
- **Primary Font**: Inter (system font fallback) for excellent readability
- **Heading Scale**: 2xl (32px), xl (24px), lg (20px), base (16px), sm (14px)
- **Font Weights**: Regular (400), Medium (500), Semibold (600), Bold (700)

### Design System Checklist

- ✅ **Consistency**: All UI elements follow consistent spacing (4px grid), colors, and typography
- ✅ **Reusability**: Components built with shadcn-ui for maximum reusability and customization
- ✅ **Clarity**: Clear visual hierarchy with proper contrast and spacing
- ✅ **Responsiveness**: Mobile-first design with breakpoints at 640px, 768px, 1024px, 1280px

### Accessibility Checklist (WCAG 2.1 AA)

- ✅ **Perceivable**: 
  - Color contrast ratios ≥4.5:1 for normal text, ≥3:1 for large text
  - Text alternatives for all images and icons
  - Scalable text up to 200% without horizontal scrolling
- ✅ **Operable**: 
  - Full keyboard navigation support
  - Focus indicators visible and clear
  - No content that flashes more than 3 times per second
- ✅ **Understandable**: 
  - Consistent navigation and interaction patterns
  - Clear error messages and form validation
  - Predictable functionality across the application
- ✅ **Robust**: 
  - Valid HTML5 semantic markup
  - Compatible with assistive technologies
  - Progressive enhancement approach

---

## Data Schema & Models

### Core Data Entities

#### Disc Entity
```typescript
interface Disc {
  id: string;                    // UUID v4
  manufacturer: string;          // e.g., "Innova", "Discraft", "Dynamic Discs"
  mold: string;                 // e.g., "Destroyer", "Buzzz", "Judge"
  plasticType: string;          // e.g., "Champion", "ESP", "Lucid"
  weight: number;               // 150-180 grams
  condition: DiscCondition;     // Enum: NEW, LIGHTLY_USED, MODERATELY_USED, WELL_USED, BEAT_IN
  flightNumbers: FlightNumbers; // Speed, Glide, Turn, Fade
  color: string;                // Primary disc color
  notes?: string;               // User notes (max 500 characters)
  purchaseDate?: Date;          // When disc was acquired
  purchasePrice?: number;       // Price paid in USD
  currentLocation: Location;    // Where disc is currently stored
  imageUrl?: string;            // Optional disc image
  createdAt: Date;              // Record creation timestamp
  updatedAt: Date;              // Last modification timestamp
}
```

#### Flight Numbers Entity
```typescript
interface FlightNumbers {
  speed: number;    // 1-14 (how fast the disc needs to be thrown)
  glide: number;    // 1-7 (how long the disc stays in the air)
  turn: number;     // -5 to +1 (high speed stability)
  fade: number;     // 0-5 (low speed stability)
}
```

#### Supporting Enums
```typescript
enum DiscCondition {
  NEW = 'new',
  LIGHTLY_USED = 'lightly_used',
  MODERATELY_USED = 'moderately_used', 
  WELL_USED = 'well_used',
  BEAT_IN = 'beat_in'
}

enum Location {
  BAG = 'bag',
  CAR = 'car',
  HOME = 'home',
  LOANED = 'loaned',
  LOST = 'lost'
}
```

### Data Validation Schema

```typescript
import { z } from 'zod';

const DiscSchema = z.object({
  id: z.string().uuid(),
  manufacturer: z.string().min(1).max(50),
  mold: z.string().min(1).max(50),
  plasticType: z.string().min(1).max(30),
  weight: z.number().min(150).max(180),
  condition: z.nativeEnum(DiscCondition),
  flightNumbers: z.object({
    speed: z.number().min(1).max(14),
    glide: z.number().min(1).max(7),
    turn: z.number().min(-5).max(1),
    fade: z.number().min(0).max(5)
  }),
  color: z.string().min(1).max(30),
  notes: z.string().max(500).optional(),
  purchaseDate: z.date().optional(),
  purchasePrice: z.number().positive().optional(),
  currentLocation: z.nativeEnum(Location),
  imageUrl: z.string().url().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
});
```

---

## Component Architecture & Design Patterns

### Atomic Design Implementation

#### Atoms (Basic UI Elements)
- **Button**: Primary, secondary, outline, ghost variants with proper focus states
- **Input**: Text, number, select with validation states and accessibility labels
- **Badge**: For disc conditions, manufacturers, and status indicators
- **Card**: Container component for disc information display
- **Avatar**: For user profile and disc manufacturer logos

#### Molecules (Simple Combinations)
- **DiscCard**: Displays disc information in grid layout
- **SearchInput**: Search field with clear button and loading state
- **FilterDropdown**: Multi-select dropdown for filtering options
- **FormField**: Input with label, validation, and error message
- **StatCard**: Displays collection statistics (total discs, by manufacturer, etc.)

#### Organisms (Complex UI Sections)
- **DiscInventoryGrid**: Responsive grid of disc cards with pagination
- **SearchFilterBar**: Combined search and filter controls
- **DiscDetailsModal**: Full disc information display and editing
- **NavigationHeader**: Main navigation with search and user actions
- **AddDiscForm**: Complete form for adding new discs

#### Pages (Complete Views)
- **InventoryPage**: Main collection view with grid and filters
- **AddDiscPage**: Dedicated page for adding new discs
- **DiscDetailPage**: Individual disc view and editing
- **SettingsPage**: Application settings and preferences

### Component Design Patterns

#### Compound Components
```typescript
// Usage example for flexible, composable components
<DiscCard>
  <DiscCard.Image src={disc.imageUrl} alt={`${disc.manufacturer} ${disc.mold}`} />
  <DiscCard.Header>
    <DiscCard.Title>{disc.manufacturer} {disc.mold}</DiscCard.Title>
    <DiscCard.Badge condition={disc.condition} />
  </DiscCard.Header>
  <DiscCard.FlightNumbers numbers={disc.flightNumbers} />
  <DiscCard.Actions>
    <DiscCard.EditButton onClick={handleEdit} />
    <DiscCard.DeleteButton onClick={handleDelete} />
  </DiscCard.Actions>
</DiscCard>
```

#### Render Props Pattern
```typescript
// For flexible data fetching and state management
<InventoryProvider>
  {({ discs, loading, error, addDisc, updateDisc, deleteDisc }) => (
    <DiscInventoryGrid 
      discs={discs}
      loading={loading}
      onEdit={updateDisc}
      onDelete={deleteDisc}
    />
  )}
</InventoryProvider>
```

---

## User Interface Layouts & Wireframes

### Mobile-First Responsive Design

#### Mobile Layout (320px - 767px)
- **Header**: Compact navigation with hamburger menu
- **Search**: Full-width search bar with filter button
- **Grid**: Single column disc cards with essential information
- **Actions**: Bottom sheet for disc actions and forms

#### Tablet Layout (768px - 1023px)
- **Header**: Expanded navigation with visible menu items
- **Search**: Search bar with inline filter controls
- **Grid**: 2-3 column disc cards with more details
- **Sidebar**: Collapsible filter panel

#### Desktop Layout (1024px+)
- **Header**: Full navigation with search and user profile
- **Sidebar**: Persistent filter panel with advanced options
- **Main**: 3-4 column disc grid with detailed cards
- **Modal**: Overlay forms and detailed views

### Key User Flows

#### Add New Disc Flow
1. **Entry Point**: "Add Disc" button (prominent placement)
2. **Form**: Step-by-step form with validation
3. **Confirmation**: Success message with option to add another
4. **Navigation**: Return to inventory with new disc highlighted

#### Search and Filter Flow
1. **Search**: Real-time search with suggestions
2. **Filters**: Progressive disclosure of filter options
3. **Results**: Immediate visual feedback with result count
4. **Clear**: Easy way to clear filters and return to full collection

#### Edit Disc Flow
1. **Selection**: Click/tap disc card to open details
2. **Edit Mode**: In-place editing or modal form
3. **Validation**: Real-time validation with clear error messages
4. **Save**: Confirmation and return to collection view

---

## Interaction Design & Micro-interactions

### Animation Principles
- **Duration**: 200-300ms for most transitions
- **Easing**: Ease-out for entering, ease-in for exiting
- **Purpose**: Provide feedback, guide attention, maintain context

### Key Micro-interactions
- **Card Hover**: Subtle elevation and shadow increase
- **Button Press**: Scale down (0.95) with haptic feedback on mobile
- **Form Validation**: Smooth error message appearance with color transition
- **Search**: Loading spinner and result count animation
- **Filter Application**: Smooth grid re-layout with stagger animation

### Loading States
- **Skeleton Screens**: For initial page load and search results
- **Progressive Loading**: Load critical content first, then enhancements
- **Optimistic Updates**: Immediate UI feedback for user actions

---

## Accessibility Design Specifications

### Keyboard Navigation
- **Tab Order**: Logical flow through interactive elements
- **Focus Management**: Clear focus indicators and skip links
- **Shortcuts**: Common keyboard shortcuts (Ctrl+F for search, Escape to close modals)

### Screen Reader Support
- **Semantic HTML**: Proper heading hierarchy and landmark regions
- **ARIA Labels**: Descriptive labels for complex interactions
- **Live Regions**: Announce dynamic content changes

### Visual Accessibility
- **Color Independence**: Information not conveyed by color alone
- **Text Scaling**: Readable at 200% zoom without horizontal scrolling
- **Motion Preferences**: Respect prefers-reduced-motion settings

---

## Performance Design Considerations

### Image Optimization
- **Lazy Loading**: Images load as they enter viewport
- **Responsive Images**: Multiple sizes for different screen densities
- **Fallbacks**: Graceful degradation when images fail to load

### Data Loading Strategies
- **Pagination**: Load discs in batches of 20-50 items
- **Virtual Scrolling**: For collections over 100 discs
- **Caching**: Intelligent caching of search results and filter states

### Bundle Optimization
- **Code Splitting**: Route-based and component-based splitting
- **Tree Shaking**: Remove unused code from production bundle
- **Compression**: Gzip compression for all text assets

---

*This design specification provides a comprehensive blueprint for creating a user-friendly, accessible, and performant disc golf inventory management system.*
