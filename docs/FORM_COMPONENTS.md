# Form Components Documentation

This document provides comprehensive documentation for the form components implemented for the Disc Golf Inventory Management System.

## Overview

The form components provide a complete solution for adding new discs to the collection with:
- **FormField**: Reusable form field component with validation and accessibility
- **AddDiscForm**: Complete disc entry form with all required fields and validation
- **Data Constants**: Manufacturer and plastic type suggestions for autocomplete

## Components

### FormField Component

A reusable form field component that provides label and input association, error message display, required field indicators, and accessible markup.

#### Features
- ✅ Proper label-input association via htmlFor/id
- ✅ Required field visual indicators with asterisk
- ✅ Error message display with ARIA support
- ✅ Help text support
- ✅ Accessible markup with proper ARIA attributes
- ✅ Responsive design
- ✅ Focus management

#### Usage

```tsx
import { FormField } from "@/components/forms";

// Basic usage
<FormField
  id="manufacturer"
  label="Manufacturer"
  placeholder="Enter manufacturer"
  required
  value={value}
  onChange={handleChange}
/>

// With error and help text
<FormField
  id="weight"
  label="Weight (grams)"
  type="number"
  placeholder="175"
  required
  error={errors.weight?.message}
  helpText="Range: 150-180g"
  value={weight}
  onChange={handleWeightChange}
/>
```

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `id` | `string` | ✅ | Unique identifier for the field |
| `label` | `string` | ✅ | Field label text |
| `type` | `React.HTMLInputTypeAttribute` | ❌ | Input type (defaults to "text") |
| `placeholder` | `string` | ❌ | Input placeholder text |
| `value` | `string \| number` | ❌ | Current field value |
| `onChange` | `(event: ChangeEvent<HTMLInputElement>) => void` | ❌ | Change handler |
| `onBlur` | `(event: FocusEvent<HTMLInputElement>) => void` | ❌ | Blur handler |
| `required` | `boolean` | ❌ | Whether the field is required |
| `disabled` | `boolean` | ❌ | Whether the field is disabled |
| `error` | `string` | ❌ | Error message to display |
| `helpText` | `string` | ❌ | Help text to display below the field |
| `className` | `string` | ❌ | Additional CSS classes for the container |
| `inputClassName` | `string` | ❌ | Additional CSS classes for the input |
| `labelClassName` | `string` | ❌ | Additional CSS classes for the label |

### FormFieldGroup Component

A component for grouping related form fields with optional title and description.

#### Usage

```tsx
import { FormFieldGroup } from "@/components/forms";

<FormFieldGroup
  title="Basic Information"
  description="Enter the basic details about your disc"
>
  <FormField id="manufacturer" label="Manufacturer" required />
  <FormField id="mold" label="Mold" required />
</FormFieldGroup>
```

### AddDiscForm Component

A complete form for adding new discs to the collection with comprehensive validation and user experience features.

#### Features
- ✅ React Hook Form with Zod validation
- ✅ Real-time validation feedback
- ✅ Manufacturer and plastic type autocomplete suggestions
- ✅ Flight numbers validation with range checking
- ✅ Form submission with error handling
- ✅ Accessible form design with proper ARIA attributes
- ✅ Responsive layout for mobile and desktop
- ✅ Success/error message display
- ✅ Form reset functionality
- ✅ Integration with useInventory hook

#### Usage

```tsx
import { AddDiscForm } from "@/components/forms";

// Basic usage
<AddDiscForm />

// With callbacks
<AddDiscForm
  onSuccess={(disc) => {
    console.log("Disc added:", disc);
    // Handle success (e.g., redirect, show notification)
  }}
  onCancel={() => {
    // Handle cancel (e.g., close modal, navigate back)
  }}
/>

// Without card wrapper
<AddDiscForm showCard={false} />
```

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `onSuccess` | `(disc: CreateDiscInput) => void` | ❌ | Callback when disc is successfully added |
| `onCancel` | `() => void` | ❌ | Callback when form is cancelled |
| `className` | `string` | ❌ | Additional CSS classes |
| `showCard` | `boolean` | ❌ | Whether to show the form in a card wrapper (default: true) |

#### Form Fields

The AddDiscForm includes the following sections and fields:

**Basic Information:**
- Manufacturer (required) - Select from predefined list
- Mold (required) - Text input
- Plastic Type (required) - Select based on manufacturer
- Weight (required) - Number input (150-180g)
- Color (required) - Select from predefined colors
- Condition (required) - Select from condition enum

**Flight Numbers:**
- Speed (required) - Number input (1-14)
- Glide (required) - Number input (1-7)
- Turn (required) - Number input (-5 to +1)
- Fade (required) - Number input (0-5)

**Additional Information (Optional):**
- Purchase Date - Date input
- Purchase Price - Number input ($0-$1000)
- Current Location (required) - Select from location enum
- Image URL - URL input
- Notes - Textarea (max 500 characters)

## Data Constants

### Manufacturer Data

The system includes a comprehensive list of disc golf manufacturers:

```tsx
import { DISC_MANUFACTURERS } from "@/lib/discData";

// Major manufacturers include:
// Innova, Discraft, Dynamic Discs, Latitude 64, Westside Discs,
// MVP Disc Sports, Axiom Discs, Prodigy Disc, Kastaplast, Discmania
```

### Plastic Types

Plastic types are organized by manufacturer for accurate suggestions:

```tsx
import { getPlasticTypesForManufacturer } from "@/lib/discData";

const plasticTypes = getPlasticTypesForManufacturer("Innova");
// Returns: ["Champion", "Star", "DX", "Pro", "GStar", ...]
```

### Utility Functions

```tsx
import { 
  searchManufacturers, 
  searchPlasticTypes,
  getAllPlasticTypes 
} from "@/lib/discData";

// Search manufacturers
const results = searchManufacturers("inn"); // Returns ["Innova"]

// Search plastic types for a manufacturer
const plastics = searchPlasticTypes("Innova", "star"); // Returns ["Star", "Halo Star"]

// Get all plastic types
const allPlastics = getAllPlasticTypes();
```

## Validation

The form uses Zod schemas for comprehensive validation:

- **Required Fields**: Manufacturer, mold, plastic type, weight, condition, flight numbers, color, current location
- **Range Validation**: Weight (150-180g), flight numbers (speed: 1-14, glide: 1-7, turn: -5 to +1, fade: 0-5)
- **Format Validation**: Email format for image URLs, character limits for text fields
- **Business Rules**: Purchase date cannot be in the future, purchase price has reasonable limits

## Accessibility

Both components follow WCAG 2.1 AA guidelines:

- ✅ Proper semantic HTML structure
- ✅ Label-input associations via htmlFor/id
- ✅ ARIA attributes for error states and descriptions
- ✅ Focus management and keyboard navigation
- ✅ Screen reader announcements for errors
- ✅ Color-independent error indication
- ✅ Required field indicators

## Integration

### With useInventory Hook

The AddDiscForm integrates seamlessly with the useInventory hook:

```tsx
const { addDisc, loading, error } = useInventory();

// The form automatically uses addDisc for submission
// and handles loading states and errors
```

### With Validation System

The form uses the existing Zod validation schemas:

```tsx
import { CreateDiscSchema } from "@/lib/validation";

// Form validation is handled automatically via React Hook Form
// with zodResolver integration
```

## File Structure

```
components/forms/
├── FormField.tsx          # Reusable form field component
├── AddDiscForm.tsx        # Complete disc entry form
└── index.ts              # Module exports

lib/
└── discData.ts           # Manufacturer and plastic type data
```

## Dependencies

- `react-hook-form` - Form state management
- `@hookform/resolvers` - Zod integration for validation
- `@radix-ui/react-label` - Accessible label component
- `@radix-ui/react-select` - Accessible select component
- `lucide-react` - Icons for UI elements

## Testing

The components have been verified for:
- ✅ TypeScript compilation without errors
- ✅ ESLint compliance (no explicit any types)
- ✅ Component rendering and functionality
- ✅ Form validation and submission
- ✅ Integration with existing hooks and utilities

## Next Steps

1. **Integration**: Add the AddDiscForm to the main application pages
2. **Testing**: Write comprehensive unit tests for form validation scenarios
3. **Enhancement**: Consider adding autocomplete for mold names based on manufacturer
4. **Performance**: Implement debounced search for large manufacturer lists
5. **UX**: Add form auto-save functionality for better user experience
