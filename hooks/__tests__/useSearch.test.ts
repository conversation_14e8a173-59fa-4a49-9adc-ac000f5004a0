/**
 * Test suite for useSearch Hook
 *
 * Comprehensive tests for search hook functionality including
 * debounced search, URL synchronization, filter management, and integration.
 */

import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";
import { renderHook, act, waitFor } from "@testing-library/react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import type { Disc } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";
import { useSearch, type UseSearchOptions } from "../useSearch";

// ============================================================================
// MOCKS
// ============================================================================

// Mock Next.js navigation hooks
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(),
  usePathname: vi.fn(),
}));

const mockRouter = {
  replace: vi.fn(),
  push: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
};

const mockSearchParams = new URLSearchParams();
const mockPathname = "/inventory";

// ============================================================================
// TEST DATA
// ============================================================================

const createTestDisc = (overrides: Partial<Disc> = {}): Disc => ({
  id: "test-id",
  manufacturer: "Innova",
  mold: "Destroyer",
  plasticType: "Champion",
  weight: 175,
  condition: DiscCondition.NEW,
  flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
  color: "Blue",
  notes: "Test disc",
  currentLocation: Location.BAG,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
  ...overrides,
});

const testDiscs: Disc[] = [
  createTestDisc({
    id: "1",
    manufacturer: "Innova",
    mold: "Destroyer",
    color: "Blue",
  }),
  createTestDisc({
    id: "2",
    manufacturer: "Discraft",
    mold: "Buzzz",
    color: "Red",
    flightNumbers: { speed: 5, glide: 4, turn: -1, fade: 1 },
  }),
  createTestDisc({
    id: "3",
    manufacturer: "Dynamic Discs",
    mold: "Judge",
    color: "Green",
    flightNumbers: { speed: 2, glide: 4, turn: 0, fade: 1 },
  }),
];

// ============================================================================
// SETUP AND TEARDOWN
// ============================================================================

beforeEach(() => {
  vi.clearAllMocks();

  (useRouter as any).mockReturnValue(mockRouter);
  (useSearchParams as any).mockReturnValue(mockSearchParams);
  (usePathname as any).mockReturnValue(mockPathname);

  // Clear search params
  mockSearchParams.forEach((_, key) => {
    mockSearchParams.delete(key);
  });
});

afterEach(() => {
  vi.clearAllTimers();
});

// ============================================================================
// BASIC FUNCTIONALITY TESTS
// ============================================================================

describe("useSearch - Basic Functionality", () => {
  it("should initialize with empty search state", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    expect(result.current.searchState.query).toBe("");
    expect(result.current.searchState.filters).toEqual({});
    expect(result.current.filteredDiscs).toHaveLength(testDiscs.length);
    expect(result.current.hasActiveSearch).toBe(false);
    expect(result.current.hasActiveFilters).toBe(false);
  });

  it("should update query and filter results", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    act(() => {
      result.current.setQuery("Destroyer");
    });

    expect(result.current.searchState.query).toBe("Destroyer");
    expect(result.current.hasActiveSearch).toBe(true);
    expect(result.current.filteredDiscs).toHaveLength(1);
    expect(result.current.filteredDiscs[0].mold).toBe("Destroyer");
  });

  it("should update filters and filter results", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    act(() => {
      result.current.setFilters({ manufacturers: ["Innova"] });
    });

    expect(result.current.searchState.filters.manufacturers).toEqual(["Innova"]);
    expect(result.current.hasActiveFilters).toBe(true);
    expect(result.current.filteredDiscs).toHaveLength(1);
  });

  it("should clear search query", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    act(() => {
      result.current.setQuery("test");
    });

    expect(result.current.searchState.query).toBe("test");

    act(() => {
      result.current.clearSearch();
    });

    expect(result.current.searchState.query).toBe("");
    expect(result.current.hasActiveSearch).toBe(false);
  });

  it("should clear filters", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    act(() => {
      result.current.setFilters({ manufacturers: ["Innova"] });
    });

    expect(result.current.hasActiveFilters).toBe(true);

    act(() => {
      result.current.clearFilters();
    });

    expect(result.current.searchState.filters).toEqual({});
    expect(result.current.hasActiveFilters).toBe(false);
  });

  it("should clear all search state", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    act(() => {
      result.current.setQuery("test");
      result.current.setFilters({ manufacturers: ["Innova"] });
    });

    expect(result.current.hasActiveSearch).toBe(true);
    expect(result.current.hasActiveFilters).toBe(true);

    act(() => {
      result.current.clearAll();
    });

    expect(result.current.searchState.query).toBe("");
    expect(result.current.searchState.filters).toEqual({});
    expect(result.current.hasActiveSearch).toBe(false);
    expect(result.current.hasActiveFilters).toBe(false);
  });
});

// ============================================================================
// DEBOUNCING TESTS
// ============================================================================

describe("useSearch - Debouncing", () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it("should debounce search queries", async () => {
    const { result } = renderHook(() => useSearch(testDiscs, { debounceDelay: 300, immediateURLUpdate: false }));

    // Set query multiple times quickly
    act(() => {
      result.current.setQuery("D");
    });

    act(() => {
      result.current.setQuery("De");
    });

    act(() => {
      result.current.setQuery("Des");
    });

    act(() => {
      result.current.setQuery("Destroyer");
    });

    // The search is applied immediately in our implementation
    // The debouncing is for URL updates, not search results
    expect(result.current.filteredDiscs).toHaveLength(1);
    expect(result.current.searchState.query).toBe("Destroyer");
  });

  it("should respect custom debounce delay", async () => {
    const { result } = renderHook(() => useSearch(testDiscs, { debounceDelay: 500 }));

    act(() => {
      result.current.setQuery("Destroyer");
    });

    // Search is applied immediately, debouncing is for URL updates
    expect(result.current.filteredDiscs).toHaveLength(1);
    expect(result.current.searchState.query).toBe("Destroyer");
  });
});

// ============================================================================
// URL SYNCHRONIZATION TESTS
// ============================================================================

describe("useSearch - URL Synchronization", () => {
  it("should load initial state from URL", () => {
    mockSearchParams.set("q", "Destroyer");
    mockSearchParams.set("manufacturers", "Innova,Discraft");

    const { result } = renderHook(() => useSearch(testDiscs));

    expect(result.current.searchState.query).toBe("Destroyer");
    expect(result.current.searchState.filters.manufacturers).toEqual(["Innova", "Discraft"]);
  });

  it("should update URL when search state changes", async () => {
    const { result } = renderHook(() => useSearch(testDiscs, { syncWithURL: true }));

    act(() => {
      result.current.setQuery("Destroyer");
    });

    await waitFor(() => {
      expect(mockRouter.replace).toHaveBeenCalledWith(expect.stringContaining("q=Destroyer"), { scroll: false });
    });
  });

  it("should not sync with URL when disabled", () => {
    const { result } = renderHook(() => useSearch(testDiscs, { syncWithURL: false }));

    act(() => {
      result.current.setQuery("Destroyer");
    });

    expect(mockRouter.replace).not.toHaveBeenCalled();
  });

  it("should update URL immediately when configured", () => {
    const { result } = renderHook(() => useSearch(testDiscs, { immediateURLUpdate: true }));

    act(() => {
      result.current.setQuery("Destroyer");
    });

    expect(mockRouter.replace).toHaveBeenCalled();
  });
});

// ============================================================================
// FILTER SUGGESTIONS TESTS
// ============================================================================

describe("useSearch - Filter Suggestions", () => {
  it("should provide filter suggestions based on disc collection", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    const suggestions = result.current.filterSuggestions;

    expect(suggestions.manufacturers).toContain("Innova");
    expect(suggestions.manufacturers).toContain("Discraft");
    expect(suggestions.manufacturers).toContain("Dynamic Discs");
    expect(suggestions.weightRange.min).toBe(175);
    expect(suggestions.weightRange.max).toBe(175);
  });

  it("should handle empty disc collection", () => {
    const emptyDiscs: Disc[] = [];
    const { result } = renderHook(() => useSearch(emptyDiscs));

    const suggestions = result.current.filterSuggestions;

    expect(suggestions.manufacturers).toEqual([]);
    expect(suggestions.weightRange).toEqual({ min: 150, max: 180 });
  });
});

// ============================================================================
// PERFORMANCE METRICS TESTS
// ============================================================================

describe("useSearch - Performance Metrics", () => {
  it("should track performance metrics when enabled", () => {
    const { result } = renderHook(() => useSearch(testDiscs, { enableMetrics: true }));

    act(() => {
      result.current.setQuery("Destroyer");
    });

    expect(result.current.searchState.metrics).toBeDefined();
    expect(result.current.searchState.metrics?.totalDiscs).toBe(testDiscs.length);
    expect(result.current.searchState.metrics?.executionTime).toBeGreaterThan(0);
  });

  it("should not track metrics when disabled", () => {
    const { result } = renderHook(() => useSearch(testDiscs, { enableMetrics: false }));

    act(() => {
      result.current.setQuery("Destroyer");
    });

    expect(result.current.searchState.metrics).toBeUndefined();
  });
});

// ============================================================================
// INTEGRATION TESTS
// ============================================================================

describe("useSearch - Integration", () => {
  it("should combine search query and filters", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    act(() => {
      result.current.setQuery("Innova");
      result.current.setFilters({ speedRange: { min: 10, max: 15 } });
    });

    // Should find Destroyer (Innova, speed 12)
    expect(result.current.filteredDiscs).toHaveLength(1);
    expect(result.current.filteredDiscs[0].mold).toBe("Destroyer");
  });

  it("should handle complex filter combinations", () => {
    const { result } = renderHook(() => useSearch(testDiscs));

    act(() => {
      result.current.setFilters({
        manufacturers: ["Innova", "Discraft"],
        speedRange: { min: 1, max: 10 },
        colors: ["Red"],
      });
    });

    // Should find only Buzzz (Discraft, speed 5, Red)
    expect(result.current.filteredDiscs).toHaveLength(1);
    expect(result.current.filteredDiscs[0].mold).toBe("Buzzz");
  });
});
