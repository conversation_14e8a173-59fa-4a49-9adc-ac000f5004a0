/**
 * useLocalStorage Hook for the Disc Golf Inventory Management System
 *
 * This hook provides reactive localStorage state management with SSR-safe implementation,
 * error boundary integration, and TypeScript generic support.
 */

"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { storage, StorageError, StorageErrorType, type StorageResult, type StorageOptions } from "@/lib/storage";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Hook configuration options
 */
export interface UseLocalStorageOptions<T> extends StorageOptions {
  /** Default value to use when no stored value exists */
  defaultValue?: T;
  /** Whether to sync across browser tabs/windows */
  syncAcrossTabs?: boolean;
  /** Custom error handler */
  onError?: (error: StorageError) => void;
  /** Whether to initialize immediately or wait for client-side hydration */
  initializeOnMount?: boolean;
}

/**
 * Hook return value
 */
export interface UseLocalStorageReturn<T> {
  /** Current value */
  value: T | undefined;
  /** Function to update the value */
  setValue: (value: T | ((prev: T | undefined) => T)) => void;
  /** Function to remove the value */
  removeValue: () => void;
  /** Loading state during initial hydration */
  loading: boolean;
  /** Error state */
  error: StorageError | null;
  /** Whether storage is available */
  isSupported: boolean;
}

// ============================================================================
// CUSTOM HOOK IMPLEMENTATION
// ============================================================================

/**
 * Custom React hook for localStorage state management
 *
 * Features:
 * - SSR-safe implementation with proper hydration
 * - Reactive updates across components
 * - Cross-tab synchronization
 * - TypeScript generic support
 * - Error boundary integration
 * - Graceful degradation when localStorage is unavailable
 *
 * @param key - Storage key
 * @param options - Hook configuration options
 * @returns Hook state and methods
 */
export function useLocalStorage<T>(key: string, options: UseLocalStorageOptions<T> = {}): UseLocalStorageReturn<T> {
  const { defaultValue, syncAcrossTabs = true, onError, initializeOnMount = true, ...storageOptions } = options;

  // State management
  const [value, setValue] = useState<T | undefined>(defaultValue);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<StorageError | null>(null);
  const [isSupported] = useState(() => storage.isStorageAvailable());

  // Refs for stable references
  const keyRef = useRef(key);
  const onErrorRef = useRef(onError);

  // Update refs when props change
  useEffect(() => {
    keyRef.current = key;
    onErrorRef.current = onError;
  }, [key, onError]);

  /**
   * Handle storage errors
   */
  const handleError = useCallback((storageError: StorageError) => {
    setError(storageError);
    if (onErrorRef.current) {
      onErrorRef.current(storageError);
    }
  }, []);

  /**
   * Read value from localStorage
   */
  const readValue = useCallback((): T | undefined => {
    if (!isSupported) {
      return defaultValue;
    }

    try {
      const result = storage.getItem<T>(keyRef.current, storageOptions);

      if (!result.success) {
        if (result.error) {
          handleError(result.error);
        }
        return defaultValue;
      }

      return result.data ?? defaultValue;
    } catch (error) {
      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Failed to read from localStorage: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      handleError(storageError);
      return defaultValue;
    }
  }, [isSupported, defaultValue, storageOptions, handleError]);

  /**
   * Write value to localStorage
   */
  const writeValue = useCallback(
    (newValue: T) => {
      if (!isSupported) {
        setValue(newValue);
        return;
      }

      try {
        const result = storage.setItem(keyRef.current, newValue, storageOptions);

        if (!result.success) {
          if (result.error) {
            handleError(result.error);
          }
          return;
        }

        setValue(newValue);
        setError(null);
      } catch (error) {
        const storageError = new StorageError(
          StorageErrorType.UNKNOWN_ERROR,
          `Failed to write to localStorage: ${error instanceof Error ? error.message : "Unknown error"}`,
          error instanceof Error ? error : undefined
        );
        handleError(storageError);
      }
    },
    [isSupported, storageOptions, handleError]
  );

  /**
   * Remove value from localStorage
   */
  const removeValue = useCallback(() => {
    if (!isSupported) {
      setValue(defaultValue);
      return;
    }

    try {
      const result = storage.removeItem(keyRef.current);

      if (!result.success) {
        if (result.error) {
          handleError(result.error);
        }
        return;
      }

      setValue(defaultValue);
      setError(null);
    } catch (error) {
      const storageError = new StorageError(
        StorageErrorType.UNKNOWN_ERROR,
        `Failed to remove from localStorage: ${error instanceof Error ? error.message : "Unknown error"}`,
        error instanceof Error ? error : undefined
      );
      handleError(storageError);
    }
  }, [isSupported, defaultValue, handleError]);

  /**
   * Update value with support for functional updates
   */
  const setStoredValue = useCallback(
    (newValue: T | ((prev: T | undefined) => T)) => {
      const valueToStore = typeof newValue === "function" ? (newValue as (prev: T | undefined) => T)(value) : newValue;

      writeValue(valueToStore);
    },
    [value, writeValue]
  );

  /**
   * Handle storage events for cross-tab synchronization
   */
  useEffect(() => {
    if (!syncAcrossTabs || !isSupported) {
      return;
    }

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === keyRef.current && e.newValue !== null) {
        try {
          const result = storage.getItem<T>(keyRef.current, storageOptions);
          if (result.success) {
            setValue(result.data ?? defaultValue);
            setError(null);
          }
        } catch (error) {
          const storageError = new StorageError(
            StorageErrorType.DESERIALIZATION_ERROR,
            `Failed to sync storage change: ${error instanceof Error ? error.message : "Unknown error"}`,
            error instanceof Error ? error : undefined
          );
          handleError(storageError);
        }
      } else if (e.key === keyRef.current && e.newValue === null) {
        setValue(defaultValue);
        setError(null);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [syncAcrossTabs, isSupported, defaultValue, storageOptions, handleError]);

  /**
   * Initialize value on mount (SSR-safe)
   */
  useEffect(() => {
    if (!initializeOnMount) {
      setLoading(false);
      return;
    }

    // Delay initialization to ensure we're on the client side
    const timeoutId = setTimeout(() => {
      const initialValue = readValue();
      setValue(initialValue);
      setLoading(false);
    }, 0);

    return () => clearTimeout(timeoutId);
  }, [initializeOnMount, readValue]);

  /**
   * Update value when key changes
   */
  useEffect(() => {
    if (!loading && initializeOnMount) {
      const newValue = readValue();
      setValue(newValue);
    }
  }, [key, loading, initializeOnMount, readValue]);

  return {
    value,
    setValue: setStoredValue,
    removeValue,
    loading,
    error,
    isSupported,
  };
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Simplified hook for boolean values
 */
export function useLocalStorageBoolean(
  key: string,
  defaultValue = false,
  options?: Omit<UseLocalStorageOptions<boolean>, "defaultValue">
) {
  return useLocalStorage(key, { ...options, defaultValue });
}

/**
 * Simplified hook for string values
 */
export function useLocalStorageString(
  key: string,
  defaultValue = "",
  options?: Omit<UseLocalStorageOptions<string>, "defaultValue">
) {
  return useLocalStorage(key, { ...options, defaultValue });
}

/**
 * Simplified hook for number values
 */
export function useLocalStorageNumber(
  key: string,
  defaultValue = 0,
  options?: Omit<UseLocalStorageOptions<number>, "defaultValue">
) {
  return useLocalStorage(key, { ...options, defaultValue });
}

/**
 * Hook for object/array values with better type inference
 */
export function useLocalStorageObject<T extends Record<string, any> | any[]>(
  key: string,
  defaultValue: T,
  options?: Omit<UseLocalStorageOptions<T>, "defaultValue">
) {
  return useLocalStorage(key, { ...options, defaultValue });
}

// Default export
export default useLocalStorage;
