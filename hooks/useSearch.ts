/**
 * useSearch Hook for the Disc Golf Inventory Management System
 *
 * This hook provides comprehensive search and filter functionality with real-time search,
 * multiple filter criteria support, URL state synchronization, and debounced search input.
 * Integrates with the enhanced filter utilities and inventory management.
 */

"use client";

import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import type { Disc } from "@/lib/types";
import {
  type EnhancedFilterCriteria,
  type FilterMetrics,
  applyAllFilters,
  createEmptyFilterCriteria,
  hasActiveFilters,
  clearAllFilters,
  serializeFiltersToURL,
  deserializeFiltersFromURL,
  getFilterSuggestions,
} from "@/lib/filterUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Search hook configuration options
 */
export interface UseSearchOptions {
  /** Debounce delay in milliseconds */
  debounceDelay?: number;
  /** Whether to sync with URL parameters */
  syncWithURL?: boolean;
  /** Whether to update URL immediately or on debounce */
  immediateURLUpdate?: boolean;
  /** Default filter criteria */
  defaultFilters?: EnhancedFilterCriteria;
  /** Whether to enable performance tracking */
  enableMetrics?: boolean;
}

/**
 * Search state interface
 */
export interface SearchState {
  /** Current search query */
  query: string;
  /** Current filter criteria */
  filters: EnhancedFilterCriteria;
  /** Whether search is currently active */
  isSearching: boolean;
  /** Performance metrics */
  metrics?: FilterMetrics;
}

/**
 * Search hook return type
 */
export interface UseSearchReturn {
  // Current state
  searchState: SearchState;
  filteredDiscs: Disc[];

  // Search actions
  setQuery: (query: string) => void;
  setFilters: (filters: EnhancedFilterCriteria | ((prev: EnhancedFilterCriteria) => EnhancedFilterCriteria)) => void;
  clearSearch: () => void;
  clearFilters: () => void;
  clearAll: () => void;

  // Utility functions
  hasActiveSearch: boolean;
  hasActiveFilters: boolean;
  filterSuggestions: ReturnType<typeof getFilterSuggestions>;

  // URL state management
  updateURL: () => void;
  loadFromURL: () => void;
}

// ============================================================================
// DEBOUNCE UTILITY
// ============================================================================

/**
 * Custom debounce hook
 *
 * @param callback - Function to debounce
 * @param delay - Debounce delay in milliseconds
 * @returns Debounced function
 */
function useDebounce<T extends (...args: any[]) => any>(callback: T, delay: number): T {
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  ) as T;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

// ============================================================================
// MAIN HOOK IMPLEMENTATION
// ============================================================================

/**
 * Custom hook for search and filter functionality
 *
 * Features:
 * - Real-time search with debouncing
 * - Multiple filter criteria support
 * - URL state synchronization
 * - Performance metrics tracking
 * - Integration with inventory management
 *
 * @param discs - Array of discs to search and filter
 * @param options - Hook configuration options
 * @returns Search state and methods
 */
export function useSearch(discs: Disc[], options: UseSearchOptions = {}): UseSearchReturn {
  const {
    debounceDelay = 300,
    syncWithURL = true,
    immediateURLUpdate = false,
    defaultFilters = createEmptyFilterCriteria(),
    enableMetrics = true,
  } = options;

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [query, setQueryState] = useState<string>("");
  const [filters, setFiltersState] = useState<EnhancedFilterCriteria>(defaultFilters);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [metrics, setMetrics] = useState<FilterMetrics | undefined>();

  // ============================================================================
  // URL STATE SYNCHRONIZATION
  // ============================================================================

  /**
   * Load search state from URL parameters
   */
  const loadFromURL = useCallback(() => {
    if (!syncWithURL) return;

    const urlFilters = deserializeFiltersFromURL(searchParams);
    const urlQuery = urlFilters.searchTerm || "";

    setQueryState(urlQuery);
    setFiltersState((prev) => ({ ...prev, ...urlFilters }));
  }, [searchParams, syncWithURL]);

  /**
   * Update URL with current search state
   */
  const updateURL = useCallback(() => {
    if (!syncWithURL) return;

    const currentFilters = { ...filters, searchTerm: query };
    const params = serializeFiltersToURL(currentFilters);
    const newURL = `${pathname}?${params.toString()}`;

    router.replace(newURL, { scroll: false });
  }, [query, filters, pathname, router, syncWithURL]);

  // Initialize from URL on mount
  useEffect(() => {
    loadFromURL();
  }, [loadFromURL]);

  // ============================================================================
  // SEARCH LOGIC
  // ============================================================================

  /**
   * Apply search and filters to disc collection
   */
  const performSearch = useCallback(() => {
    setIsSearching(true);

    const searchCriteria: EnhancedFilterCriteria = {
      ...filters,
      searchTerm: query.trim() || undefined,
    };

    const result = applyAllFilters(discs, searchCriteria);

    if (enableMetrics) {
      setMetrics(result.metrics);
    }

    setIsSearching(false);
    return result.discs;
  }, [discs, query, filters, enableMetrics]);

  // Debounced search function
  const debouncedSearch = useDebounce(performSearch, debounceDelay);

  // Memoized filtered results
  const filteredDiscs = useMemo(() => {
    return performSearch();
  }, [performSearch]);

  // ============================================================================
  // ACTION HANDLERS
  // ============================================================================

  /**
   * Update search query
   */
  const setQuery = useCallback(
    (newQuery: string) => {
      setQueryState(newQuery);

      if (immediateURLUpdate) {
        updateURL();
      } else {
        debouncedSearch();
      }
    },
    [immediateURLUpdate, updateURL, debouncedSearch]
  );

  /**
   * Update filter criteria
   */
  const setFilters = useCallback(
    (newFilters: EnhancedFilterCriteria | ((prev: EnhancedFilterCriteria) => EnhancedFilterCriteria)) => {
      setFiltersState(newFilters);

      if (immediateURLUpdate) {
        updateURL();
      }
    },
    [immediateURLUpdate, updateURL]
  );

  /**
   * Clear search query only
   */
  const clearSearch = useCallback(() => {
    setQueryState("");
    if (syncWithURL) updateURL();
  }, [syncWithURL, updateURL]);

  /**
   * Clear filters only
   */
  const clearFilters = useCallback(() => {
    setFiltersState(createEmptyFilterCriteria());
    if (syncWithURL) updateURL();
  }, [syncWithURL, updateURL]);

  /**
   * Clear both search and filters
   */
  const clearAll = useCallback(() => {
    setQueryState("");
    setFiltersState(createEmptyFilterCriteria());
    if (syncWithURL) updateURL();
  }, [syncWithURL, updateURL]);

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const hasActiveSearch = useMemo(() => query.trim().length > 0, [query]);
  const hasActiveFiltersValue = useMemo(() => hasActiveFilters(filters), [filters]);
  const filterSuggestions = useMemo(() => getFilterSuggestions(discs), [discs]);

  const searchState: SearchState = useMemo(
    () => ({
      query,
      filters,
      isSearching,
      metrics: enableMetrics ? metrics : undefined,
    }),
    [query, filters, isSearching, enableMetrics, metrics]
  );

  // ============================================================================
  // URL SYNC EFFECT
  // ============================================================================

  // Update URL when search state changes (debounced)
  useEffect(() => {
    if (syncWithURL && !immediateURLUpdate) {
      const timeoutId = setTimeout(updateURL, debounceDelay);
      return () => clearTimeout(timeoutId);
    }
  }, [query, filters, syncWithURL, immediateURLUpdate, updateURL, debounceDelay]);

  return {
    searchState,
    filteredDiscs,
    setQuery,
    setFilters,
    clearSearch,
    clearFilters,
    clearAll,
    hasActiveSearch,
    hasActiveFilters: hasActiveFiltersValue,
    filterSuggestions,
    updateURL,
    loadFromURL,
  };
}
